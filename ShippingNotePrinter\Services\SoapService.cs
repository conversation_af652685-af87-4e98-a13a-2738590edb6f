using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Net;
using System.Text;
using System.Xml;
using ShippingNotePrinter.Models;

namespace ShippingNotePrinter.Services
{
    /// <summary>
    /// SOAP服务客户端，用于调用Tomcat中的POJO Axis2服务
    /// </summary>
    public class SoapService
    {
        private readonly string _serviceUrl;
        private readonly int _timeout;

        public SoapService()
        {
            _serviceUrl = ConfigurationManager.AppSettings["SoapServiceUrl"] ?? "http://localhost:8080/axis2/services/ShippingNoteService";
            _timeout = 30000; // 30秒超时
        }

        /// <summary>
        /// 根据手机号查询发运单列表
        /// </summary>
        /// <param name="phoneNumber">手机号</param>
        /// <returns>发运单列表</returns>
        public List<ShippingNote> GetShippingNotesByPhone(string phoneNumber)
        {
            try
            {
                // 调试模式：直接返回测试数据
                System.Threading.Thread.Sleep(1000); // 模拟网络延迟
                return ParseShippingNotesResponse(""); // 传入空字符串，ParseShippingNotesResponse会返回测试数据

                // 实际部署时启用以下代码
                /*
                string soapEnvelope = CreateGetShippingNotesSoapEnvelope(phoneNumber);
                string response = SendSoapRequest(soapEnvelope, "getShippingNotesByPhone");
                return ParseShippingNotesResponse(response);
                */
            }
            catch (Exception ex)
            {
                throw new Exception($"查询发运单失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据发运单号获取详细信息
        /// </summary>
        /// <param name="shippingNoteNumber">发运单号</param>
        /// <returns>发运单详细信息</returns>
        public ShippingNote GetShippingNoteDetail(string shippingNoteNumber)
        {
            try
            {
                // 调试模式：直接返回测试数据
                System.Threading.Thread.Sleep(500); // 模拟网络延迟
                return CreateTestShippingNoteDetail(shippingNoteNumber);

                // 实际部署时启用以下代码
                /*
                string soapEnvelope = CreateGetShippingNoteDetailSoapEnvelope(shippingNoteNumber);
                string response = SendSoapRequest(soapEnvelope, "getShippingNoteDetail");
                return ParseShippingNoteDetailResponse(response);
                */
            }
            catch (Exception ex)
            {
                throw new Exception($"获取发运单详情失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建查询发运单的SOAP信封
        /// </summary>
        private string CreateGetShippingNotesSoapEnvelope(string phoneNumber)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
<soap:Envelope xmlns:soap=""http://schemas.xmlsoap.org/soap/envelope/"" 
               xmlns:ship=""http://service.shipping.com"">
    <soap:Header/>
    <soap:Body>
        <ship:getShippingNotesByPhone>
            <ship:phoneNumber>{phoneNumber}</ship:phoneNumber>
        </ship:getShippingNotesByPhone>
    </soap:Body>
</soap:Envelope>";
        }

        /// <summary>
        /// 创建获取发运单详情的SOAP信封
        /// </summary>
        private string CreateGetShippingNoteDetailSoapEnvelope(string shippingNoteNumber)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
<soap:Envelope xmlns:soap=""http://schemas.xmlsoap.org/soap/envelope/"" 
               xmlns:ship=""http://service.shipping.com"">
    <soap:Header/>
    <soap:Body>
        <ship:getShippingNoteDetail>
            <ship:shippingNoteNumber>{shippingNoteNumber}</ship:shippingNoteNumber>
        </ship:getShippingNoteDetail>
    </soap:Body>
</soap:Envelope>";
        }

        /// <summary>
        /// 发送SOAP请求
        /// </summary>
        private string SendSoapRequest(string soapEnvelope, string soapAction)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(_serviceUrl);
            request.Method = "POST";
            request.ContentType = "text/xml; charset=utf-8";
            request.Headers.Add("SOAPAction", $"\"{soapAction}\"");
            request.Timeout = _timeout;

            // 写入请求数据
            byte[] data = Encoding.UTF8.GetBytes(soapEnvelope);
            request.ContentLength = data.Length;

            using (Stream requestStream = request.GetRequestStream())
            {
                requestStream.Write(data, 0, data.Length);
            }

            // 获取响应
            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                return reader.ReadToEnd();
            }
        }

        /// <summary>
        /// 解析发运单列表响应
        /// </summary>
        private List<ShippingNote> ParseShippingNotesResponse(string xmlResponse)
        {
            List<ShippingNote> shippingNotes = new List<ShippingNote>();

            try
            {
                // 为了调试目的，暂时返回测试数据
                // 实际部署时需要解析真实的SOAP响应
                shippingNotes.Add(new ShippingNote
                {
                    ShippingNoteNumber = "SHPN01202050801597",
                    CustomerUnit = "公元管道（重庆）有限公司",
                    ShippingWarehouse = "公司总库",
                    DeliveryMethod = "送货",
                    ShippingDate = DateTime.Now.AddDays(-1),
                    ContactPerson = "小严",
                    Phone = "023-49211551",
                    MarketArea = "公司总库",
                    DeliveryAddress = "重庆市渝北区龙兴大道2408号"
                });

                shippingNotes.Add(new ShippingNote
                {
                    ShippingNoteNumber = "SHPN01202050801598",
                    CustomerUnit = "公元管道（重庆）有限公司",
                    ShippingWarehouse = "分库",
                    DeliveryMethod = "自提",
                    ShippingDate = DateTime.Now,
                    ContactPerson = "张三",
                    Phone = "023-49211552",
                    MarketArea = "分库区域",
                    DeliveryAddress = "重庆市渝北区龙兴大道2409号"
                });

                // 原始解析代码（注释掉，调试完成后可以启用）
                /*
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                XmlNamespaceManager nsManager = new XmlNamespaceManager(doc.NameTable);
                nsManager.AddNamespace("soap", "http://schemas.xmlsoap.org/soap/envelope/");
                nsManager.AddNamespace("ship", "http://service.shipping.com");

                XmlNodeList noteNodes = doc.SelectNodes("//ship:shippingNote", nsManager);

                foreach (XmlNode noteNode in noteNodes)
                {
                    ShippingNote note = new ShippingNote
                    {
                        ShippingNoteNumber = GetNodeValue(noteNode, "shippingNoteNumber"),
                        CustomerUnit = GetNodeValue(noteNode, "customerUnit"),
                        ShippingWarehouse = GetNodeValue(noteNode, "shippingWarehouse"),
                        DeliveryMethod = GetNodeValue(noteNode, "deliveryMethod"),
                        ContactPerson = GetNodeValue(noteNode, "contactPerson"),
                        Phone = GetNodeValue(noteNode, "phone"),
                        MarketArea = GetNodeValue(noteNode, "marketArea"),
                        DeliveryAddress = GetNodeValue(noteNode, "deliveryAddress")
                    };

                    if (DateTime.TryParse(GetNodeValue(noteNode, "shippingDate"), out DateTime shippingDate))
                        note.ShippingDate = shippingDate;

                    shippingNotes.Add(note);
                }
                */
            }
            catch (Exception ex)
            {
                throw new Exception($"解析发运单列表响应失败: {ex.Message}", ex);
            }

            return shippingNotes;
        }

        /// <summary>
        /// 解析发运单详情响应
        /// </summary>
        private ShippingNote ParseShippingNoteDetailResponse(string xmlResponse)
        {
            try
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                XmlNamespaceManager nsManager = new XmlNamespaceManager(doc.NameTable);
                nsManager.AddNamespace("soap", "http://schemas.xmlsoap.org/soap/envelope/");
                nsManager.AddNamespace("ns", "http://service.company.com");

                // 查找响应中的发运单详细信息
                XmlNode responseNode = doc.SelectSingleNode("//ns:getShippingNoteDetailResponse", nsManager);
                if (responseNode != null)
                {
                    XmlNode returnNode = responseNode.SelectSingleNode("ns:return", nsManager);
                    if (returnNode != null)
                    {
                        // 检查是否成功
                        XmlNode successNode = returnNode.SelectSingleNode("ns:success", nsManager);
                        if (successNode != null && successNode.InnerText.ToLower() == "true")
                        {
                            // 解析发运单详细信息
                            XmlNode detailNode = returnNode.SelectSingleNode("ns:shippingNoteDetail", nsManager);
                            if (detailNode != null)
                            {
                                ShippingNote note = new ShippingNote
                                {
                                    ShippingNoteNumber = GetNodeValue(detailNode, "ns:shippingNoteNumber", nsManager),
                                    CustomerUnit = GetNodeValue(detailNode, "ns:customerUnit", nsManager),
                                    ShippingWarehouse = GetNodeValue(detailNode, "ns:shippingWarehouse", nsManager),
                                    DeliveryMethod = GetNodeValue(detailNode, "ns:deliveryMethod", nsManager),
                                    OrderNumber = GetNodeValue(detailNode, "ns:orderNumber", nsManager),
                                    PrintNumber = GetNodeValue(detailNode, "ns:printNumber", nsManager),
                                    ContactPerson = GetNodeValue(detailNode, "ns:contactPerson", nsManager),
                                    Phone = GetNodeValue(detailNode, "ns:phone", nsManager),
                                    MarketArea = GetNodeValue(detailNode, "ns:marketArea", nsManager),
                                    DeliveryAddress = GetNodeValue(detailNode, "ns:deliveryAddress", nsManager),
                                    Remarks = GetNodeValue(detailNode, "ns:remarks", nsManager),
                                    VehicleLength = GetNodeValue(detailNode, "ns:vehicleLength", nsManager),
                                    Salesperson = GetNodeValue(detailNode, "ns:salesperson", nsManager),
                                    Supervisor = GetNodeValue(detailNode, "ns:supervisor", nsManager),
                                    Creator = GetNodeValue(detailNode, "ns:creator", nsManager),
                                    Reviewer = GetNodeValue(detailNode, "ns:reviewer", nsManager),
                                    QRCodeContent = GetNodeValue(detailNode, "ns:qrCodeContent", nsManager),
                                    BarcodeContent = GetNodeValue(detailNode, "ns:barcodeContent", nsManager)
                                };

                                // 解析日期
                                string shippingDateStr = GetNodeValue(detailNode, "ns:shippingDate", nsManager);
                                if (DateTime.TryParse(shippingDateStr, out DateTime shippingDate))
                                    note.ShippingDate = shippingDate;

                                string createDateStr = GetNodeValue(detailNode, "ns:createDate", nsManager);
                                if (DateTime.TryParse(createDateStr, out DateTime createDate))
                                    note.CreateDate = createDate;

                                // 解析数值
                                string manualMetersStr = GetNodeValue(detailNode, "ns:manualMeters", nsManager);
                                if (decimal.TryParse(manualMetersStr, out decimal manualMeters))
                                    note.ManualMeters = manualMeters;

                                string cargoWeightStr = GetNodeValue(detailNode, "ns:cargoWeight", nsManager);
                                if (decimal.TryParse(cargoWeightStr, out decimal cargoWeight))
                                    note.CargoWeight = cargoWeight;

                                // 解析布尔值
                                string isUrgentStr = GetNodeValue(detailNode, "ns:isUrgent", nsManager);
                                if (bool.TryParse(isUrgentStr, out bool isUrgent))
                                    note.IsUrgent = isUrgent;

                                // 解析明细项目
                                XmlNodeList itemNodes = detailNode.SelectNodes("ns:items", nsManager);
                                foreach (XmlNode itemNode in itemNodes)
                                {
                                    ShippingNoteItem item = ParseShippingNoteItem(itemNode, nsManager);
                                    note.Items.Add(item);
                                }

                                return note;
                            }
                        }
                        else
                        {
                            // 获取错误消息
                            XmlNode messageNode = returnNode.SelectSingleNode("ns:message", nsManager);
                            string errorMessage = messageNode?.InnerText ?? "未知错误";
                            throw new Exception($"服务返回错误: {errorMessage}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"解析发运单详情响应失败: {ex.Message}", ex);
            }

            return new ShippingNote();
        }

        /// <summary>
        /// 解析发运单明细项目
        /// </summary>
        private ShippingNoteItem ParseShippingNoteItem(XmlNode itemNode, XmlNamespaceManager nsManager)
        {
            ShippingNoteItem item = new ShippingNoteItem
            {
                ProductSpecification = GetNodeValue(itemNode, "ns:productSpecification", nsManager),
                Unit = GetNodeValue(itemNode, "ns:unit", nsManager),
                Remarks = GetNodeValue(itemNode, "ns:remarks", nsManager)
            };

            // 解析数值字段
            string seqStr = GetNodeValue(itemNode, "ns:sequenceNumber", nsManager);
            if (int.TryParse(seqStr, out int sequenceNumber))
                item.SequenceNumber = sequenceNumber;

            string qtyStr = GetNodeValue(itemNode, "ns:quantity", nsManager);
            if (decimal.TryParse(qtyStr, out decimal quantity))
                item.Quantity = quantity;

            string unitWeightStr = GetNodeValue(itemNode, "ns:unitWeight", nsManager);
            if (decimal.TryParse(unitWeightStr, out decimal unitWeight))
                item.UnitWeight = unitWeight;

            // 标长/容量现在是字符串类型
            item.StandardLength = GetNodeValue(itemNode, "ns:standardLength", nsManager);

            string countStr = GetNodeValue(itemNode, "ns:count", nsManager);
            if (decimal.TryParse(countStr, out decimal count))
                item.Count = count;

            string unitVolumeStr = GetNodeValue(itemNode, "ns:unitVolume", nsManager);
            if (decimal.TryParse(unitVolumeStr, out decimal unitVolume))
                item.UnitVolume = unitVolume;

            string amountStr = GetNodeValue(itemNode, "ns:amount", nsManager);
            if (decimal.TryParse(amountStr, out decimal amount))
                item.Amount = amount;

            return item;
        }

        /// <summary>
        /// 获取XML节点值
        /// </summary>
        private string GetNodeValue(XmlNode parentNode, string nodeName, XmlNamespaceManager nsManager)
        {
            XmlNode node = parentNode.SelectSingleNode(nodeName, nsManager);
            return node?.InnerText ?? string.Empty;
        }

        /// <summary>
        /// 获取XML节点值（无命名空间）
        /// </summary>
        private string GetNodeValue(XmlNode parentNode, string nodeName)
        {
            XmlNode node = parentNode.SelectSingleNode(nodeName);
            return node?.InnerText ?? string.Empty;
        }

        /// <summary>
        /// 创建测试发运单详细信息
        /// </summary>
        private ShippingNote CreateTestShippingNoteDetail(string shippingNoteNumber)
        {
            var note = new ShippingNote
            {
                ShippingNoteNumber = shippingNoteNumber,
                CustomerUnit = "公元管道（重庆）有限公司",
                ShippingWarehouse = "公司总库",
                DeliveryMethod = "送货",
                ShippingDate = DateTime.Now.AddDays(-1),
                OrderNumber = "SON01250050293",
                PrintNumber = "SON01250050293",
                ContactPerson = "小严",
                Phone = "023-49211551",
                MarketArea = "公司总库",
                DeliveryAddress = "重庆市渝北区龙兴大道2408号公元管道（重庆）有限公司",
                Remarks = "请提货单发货定制来料包装",
                LoadingNumber = "123-公元PP-R防冻管件",
                CargoWeight = 1718.4174m,
                Salesperson = "王五",
                Supervisor = "主管",
                Creator = "制单",
                Reviewer = "防疫",
                CreateDate = DateTime.Now.AddDays(-2),
                IsUrgent = true,
                QRCodeContent = shippingNoteNumber,
                BarcodeContent = "SON01250050293"
            };

            // 添加测试明细项目 - 包含不同的MATERIALNAME1和MATERIALSHORTNAME用于测试分页和分组
            note.Items.Add(new ShippingNoteItem
            {
                SequenceNumber = 1,
                ProductSpecification = "115-公元PP-R防冻管件",
                Unit = "箱",
                Quantity = 14,
                Count = 55,
                UnitWeight = 6260,
                Weight123 = 344.3m,
                StandardLength = "6740",
                UnitVolume = 0.123m,
                TotalVolume = 6.765m,
                Amount = 9.646m,
                MaterialName1 = "PP-R管件", // 第一页
                MaterialShortName = "防冻管件"
            });

            note.Items.Add(new ShippingNoteItem
            {
                SequenceNumber = 2,
                ProductSpecification = "公元PPR防冻弯头管件（橙色）三通 φ 20",
                Unit = "箱",
                Quantity = 1,
                Count = 48,
                UnitWeight = 320,
                Weight123 = 15.36m,
                StandardLength = "320",
                UnitVolume = 0.067m,
                TotalVolume = 3.216m,
                Amount = 0.04440m,
                MaterialName1 = "PP-R管件", // 第一页
                MaterialShortName = "防冻弯头"
            });

            note.Items.Add(new ShippingNoteItem
            {
                SequenceNumber = 3,
                ProductSpecification = "公元PPR防冻弯头管件（橙色）异径三通 φ 25*20",
                Unit = "箱",
                Quantity = 6100,
                Count = 36,
                UnitWeight = 240,
                Weight123 = 8.64m,
                StandardLength = "100",
                UnitVolume = 0.045m,
                TotalVolume = 1.620m,
                Amount = 112.85m,
                MaterialName1 = "PP-R管件", // 第一页
                MaterialShortName = "防冻弯头"
            });

            // 添加第二页的测试数据（不同的MATERIALNAME1）
            note.Items.Add(new ShippingNoteItem
            {
                SequenceNumber = 4,
                ProductSpecification = "公元PVC排水管 φ 110*3.2",
                Unit = "根",
                Quantity = 20,
                Count = 20,
                UnitWeight = 1200,
                Weight123 = 24.0m,
                StandardLength = "4000",
                UnitVolume = 0.15m,
                TotalVolume = 3.0m,
                Amount = 45.0m,
                MaterialName1 = "PVC排水管", // 第二页
                MaterialShortName = "排水管"
            });

            note.Items.Add(new ShippingNoteItem
            {
                SequenceNumber = 5,
                ProductSpecification = "公元PVC排水管 φ 160*4.0",
                Unit = "根",
                Quantity = 15,
                Count = 15,
                UnitWeight = 2000,
                Weight123 = 30.0m,
                StandardLength = "4000",
                UnitVolume = 0.25m,
                TotalVolume = 3.75m,
                Amount = 67.5m,
                MaterialName1 = "PVC排水管", // 第二页
                MaterialShortName = "排水管"
            });

            // 添加第三页的测试数据（第三种MATERIALNAME1）
            note.Items.Add(new ShippingNoteItem
            {
                SequenceNumber = 6,
                ProductSpecification = "公元PE给水管 φ 32*2.4",
                Unit = "米",
                Quantity = 100,
                Count = 100,
                UnitWeight = 180,
                Weight123 = 18.0m,
                StandardLength = "1",
                UnitVolume = 0.002m,
                TotalVolume = 0.2m,
                Amount = 25.0m,
                MaterialName1 = "PE给水管", // 第三页
                MaterialShortName = "给水管"
            });

            return note;
        }
    }
}
