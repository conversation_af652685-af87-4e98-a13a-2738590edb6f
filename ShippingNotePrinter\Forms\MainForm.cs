using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ShippingNotePrinter.Models;
using ShippingNotePrinter.Services;
using ShippingNotePrinter.Forms;

namespace ShippingNotePrinter.Forms
{
    /// <summary>
    /// 主窗体 - 发运单打印系统
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly SoapService _soapService;
        private readonly SmsService _smsService;
        private readonly PrintService _printService;
        private readonly PostBillService _postBillService;
        private List<ShippingNote> _shippingNotes;
        private int _currentStep;
        private string _currentPhoneNumber;

        // 验证码发送倒计时相关
        private Timer _countdownTimer;
        private int _countdownSeconds;
        private string _originalSendButtonText;

        public MainForm()
        {
            InitializeComponent();

            // 初始化服务
            _soapService = new SoapService();
            _smsService = new SmsService();
            _printService = new PrintService();
            _postBillService = new PostBillService();

            // 初始化倒计时Timer
            InitializeCountdownTimer();

            // 初始化界面
            InitializeUI();

            // 添加窗体大小变化事件处理
            this.Resize += MainForm_Resize;
            this.Load += MainForm_Load;
        }

        /// <summary>
        /// 初始化倒计时Timer
        /// </summary>
        private void InitializeCountdownTimer()
        {
            _countdownTimer = new Timer();
            _countdownTimer.Interval = 1000; // 1秒
            _countdownTimer.Tick += CountdownTimer_Tick;
            _originalSendButtonText = "发送验证码";
        }

        /// <summary>
        /// 倒计时Timer事件处理
        /// </summary>
        private void CountdownTimer_Tick(object sender, EventArgs e)
        {
            _countdownSeconds--;

            if (_countdownSeconds <= 0)
            {
                // 倒计时结束，恢复按钮
                _countdownTimer.Stop();
                if (btnSendCode != null)
                {
                    btnSendCode.Enabled = true;
                    btnSendCode.Text = _originalSendButtonText;
                }

                // 更新状态信息
                if (_currentStep == 1)
                {
                    UpdateStatus("请输入手机号码");
                }
                else if (_currentStep == 2)
                {
                    UpdateStatus("请输入验证码");
                }
            }
            else
            {
                // 更新按钮文本显示倒计时
                if (btnSendCode != null)
                {
                    btnSendCode.Text = $"重新发送({_countdownSeconds}s)";
                }

                // 更新状态信息
                if (_currentStep == 1)
                {
                    UpdateStatus($"验证码已发送，{_countdownSeconds}秒后可重新发送");
                }
                else if (_currentStep == 2)
                {
                    UpdateStatus($"验证码已发送，{_countdownSeconds}秒后可重新发送");
                }
            }
        }

        /// <summary>
        /// 开始倒计时
        /// </summary>
        private void StartCountdown(int seconds = 60)
        {
            _countdownSeconds = seconds;
            if (btnSendCode != null)
            {
                btnSendCode.Enabled = false;
                btnSendCode.Text = $"重新发送({_countdownSeconds}s)";
            }
            _countdownTimer.Start();
        }

        /// <summary>
        /// 初始化界面
        /// </summary>
        private void InitializeUI()
        {
            _currentStep = 0; // 0表示主界面
            _shippingNotes = new List<ShippingNote>();

            // 应用现代化样式
            ApplyModernStyles();

            // 设置自定义图标绘制
            SetupCustomIcons();

            // 显示主界面
            ShowMainInterface();

            // 设置触控友好的界面
            SetupTouchFriendlyUI();

            // 启动时间更新定时器
            StartTimeUpdater();
        }

        /// <summary>
        /// 设置自定义图标绘制
        /// </summary>
        private void SetupCustomIcons()
        {
            // 为手机按钮添加自定义绘制
            if (btnPhoneInput != null)
            {
                btnPhoneInput.Paint += DrawPhoneIcon;
                // 彻底禁用悬停效果 - 设置为卡片的背景色
                var cardColor = Color.FromArgb(74, 144, 226); // 手机验证卡片的蓝色
                btnPhoneInput.FlatAppearance.MouseOverBackColor = cardColor;
                btnPhoneInput.FlatAppearance.MouseDownBackColor = cardColor;
            }
        }

        /// <summary>
        /// 绘制手机图标
        /// </summary>
        private void DrawPhoneIcon(object sender, PaintEventArgs e)
        {
            var btn = sender as Button;
            if (btn == null) return;

            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // 绘制简化的手机图标
            var iconRect = new Rectangle(40, 25, 20, 35);
            using (var phoneBrush = new SolidBrush(Color.White))
            using (var phonePen = new Pen(Color.White, 2))
            {
                // 手机外框
                DrawRoundedRectangle(g, phonePen, iconRect, 4);
                
                // 屏幕区域
                var screenRect = new Rectangle(iconRect.X + 3, iconRect.Y + 5, iconRect.Width - 6, iconRect.Height - 10);
                g.FillRectangle(phoneBrush, screenRect);
                
                // Home按钮
                var homeRect = new Rectangle(iconRect.X + iconRect.Width/2 - 1, iconRect.Bottom - 3, 2, 2);
                g.FillEllipse(phoneBrush, homeRect);
            }

            // 绘制文字
            var textRect = new Rectangle(75, 0, btn.Width - 75, btn.Height);
            using (var textBrush = new SolidBrush(Color.White))
            {
                var stringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString("手机验证", btn.Font, textBrush, textRect, stringFormat);
            }
        }

        /// <summary>
        /// 绘制圆角矩形（扩展方法）
        /// </summary>
        private void DrawRoundedRectangle(Graphics g, Pen pen, Rectangle rect, int radius)
        {
            using (var path = CreateRoundedRectanglePath(rect, radius))
            {
                g.DrawPath(pen, path);
            }
        }

        /// <summary>
        /// 创建圆角矩形路径
        /// </summary>
        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        /// <summary>
        /// 显示主界面（只显示手机输入选项）
        /// </summary>
        private void ShowMainInterface()
        {
            // 隐藏所有步骤面板
            if (panelStep1 != null) panelStep1.Visible = false;
            if (panelStep2 != null) panelStep2.Visible = false;
            if (panelStep3 != null) panelStep3.Visible = false;
            if (panelPhoneInput != null) panelPhoneInput.Visible = false;
            if (panelVerification != null) panelVerification.Visible = false;
            if (panelNotesList != null) panelNotesList.Visible = false;

            // 清空输入框内容
            if (txtPhoneNumber != null) txtPhoneNumber.Text = "";
            if (txtVerificationCode != null) txtVerificationCode.Text = "";

            // 显示主界面卡片容器
            if (panelCardContainer != null)
            {
                panelCardContainer.Visible = true;
                // 只显示手机输入卡片，隐藏其他卡片
                ShowOnlyPhoneInputCard();
            }

            UpdateStatus("请输入手机号码开始验证");
        }

        /// <summary>
        /// 只显示手机输入卡片
        /// </summary>
        private void ShowOnlyPhoneInputCard()
        {
            // 只显示手机输入卡片，隐藏其他所有卡片
            if (cardPhoneInput != null)
            {
                cardPhoneInput.Visible = true;
                cardPhoneInput.Enabled = true;
            }

            if (cardVerifyCode != null) cardVerifyCode.Visible = false;
            if (cardSelectNotes != null) cardSelectNotes.Visible = false;
            if (cardPrintPreview != null) cardPrintPreview.Visible = false;
            if (cardPrintNotes != null) cardPrintNotes.Visible = false;
            if (cardHelp != null) cardHelp.Visible = false;
        }

        /// <summary>
        /// 重置卡片状态
        /// </summary>
        private void ResetCardStates()
        {
            // 重置所有卡片为可见状态（用于其他需要的场景）
            if (cardPhoneInput != null)
            {
                cardPhoneInput.Visible = true;
                cardPhoneInput.Enabled = true;
            }

            if (cardVerifyCode != null)
            {
                cardVerifyCode.Visible = true;
                cardVerifyCode.Enabled = false;
            }

            if (cardSelectNotes != null)
            {
                cardSelectNotes.Visible = true;
                cardSelectNotes.Enabled = false;
            }

            if (cardPrintPreview != null)
            {
                cardPrintPreview.Visible = true;
                cardPrintPreview.Enabled = false;
            }

            if (cardPrintNotes != null)
            {
                cardPrintNotes.Visible = true;
                cardPrintNotes.Enabled = false;
            }

            if (cardHelp != null)
            {
                cardHelp.Visible = true;
                cardHelp.Enabled = true;
            }
        }

        /// <summary>
        /// 设置卡片透明度
        /// </summary>
        private void SetCardOpacity(Panel card, float opacity)
        {
            // 通过调整背景色的Alpha值来模拟透明度效果
            var originalColor = card.BackColor;
            int alpha = (int)(255 * opacity);
            card.BackColor = Color.FromArgb(alpha, originalColor.R, originalColor.G, originalColor.B);
        }

        /// <summary>
        /// 启动时间更新器
        /// </summary>
        private void StartTimeUpdater()
        {
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 1000; // 每秒更新
            timer.Tick += (s, e) => {
                lblTime.Text = DateTime.Now.ToString("HH:mm:ss");
            };
            timer.Start();
        }

        /// <summary>
        /// 应用现代化样式
        /// </summary>
        private void ApplyModernStyles()
        {
            // 应用简化的卡片样式（不使用复杂的圆角和阴影，避免显示问题）
            ApplySimpleCardStyles();

            // 应用按钮样式
            ApplyModernButtonStyles();

            // 应用ListView样式
            if (listViewShippingNotes != null)
                ModernStyleManager.ApplyModernListViewStyle(listViewShippingNotes);

            // 设置主背景
            if (panelMain != null)
                panelMain.BackColor = ModernStyleManager.Colors.Background;
        }

        /// <summary>
        /// 应用简化的卡片样式
        /// </summary>
        private void ApplySimpleCardStyles()
        {
            // 为每个卡片应用简单但美观的样式
            ApplySimpleCardStyle(cardPhoneInput, Color.FromArgb(74, 144, 226));
            ApplySimpleCardStyle(cardVerifyCode, Color.FromArgb(142, 68, 173));
            ApplySimpleCardStyle(cardSelectNotes, Color.FromArgb(39, 174, 96));
            ApplySimpleCardStyle(cardPrintPreview, Color.FromArgb(243, 156, 18));
            ApplySimpleCardStyle(cardPrintNotes, Color.FromArgb(231, 76, 60));
            ApplySimpleCardStyle(cardHelp, Color.FromArgb(52, 152, 219));
        }

        /// <summary>
        /// 应用简单的卡片样式
        /// </summary>
        private void ApplySimpleCardStyle(Panel card, Color backgroundColor)
        {
            if (card == null) return;

            card.BackColor = backgroundColor;
            card.Cursor = Cursors.Hand;
            
            // 不添加悬停效果，保持背景色不变
            // 只改变鼠标指针样式表示可点击
            
            // 禁用卡片内所有按钮的悬停效果
            foreach (Control control in card.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatAppearance.MouseOverBackColor = backgroundColor;
                    btn.FlatAppearance.MouseDownBackColor = backgroundColor;
                }
            }
        }

        /// <summary>
        /// 应用现代化按钮样式
        /// </summary>
        private void ApplyModernButtonStyles()
        {
            // 主要操作按钮
            if (btnSendCode != null)
                ModernStyleManager.ApplyModernButtonStyle(btnSendCode, ModernStyleManager.ButtonStyle.Primary);
            if (btnVerifyCode != null)
                ModernStyleManager.ApplyModernButtonStyle(btnVerifyCode, ModernStyleManager.ButtonStyle.Primary);
            if (btnPrint != null)
                ModernStyleManager.ApplyModernButtonStyle(btnPrint, ModernStyleManager.ButtonStyle.Primary);

            // 成功/预览按钮
            if (btnPreview != null)
                ModernStyleManager.ApplyModernButtonStyle(btnPreview, ModernStyleManager.ButtonStyle.Success);
            if (btnConfirmSelection != null)
                ModernStyleManager.ApplyModernButtonStyle(btnConfirmSelection, ModernStyleManager.ButtonStyle.Success);

            // 次要按钮
            if (btnBackToMain != null)
                ModernStyleManager.ApplyModernButtonStyle(btnBackToMain, ModernStyleManager.ButtonStyle.Secondary);
            if (btnBackToMain2 != null)
                ModernStyleManager.ApplyModernButtonStyle(btnBackToMain2, ModernStyleManager.ButtonStyle.Secondary);
            if (btnBackToMain3 != null)
                ModernStyleManager.ApplyModernButtonStyle(btnBackToMain3, ModernStyleManager.ButtonStyle.Secondary);

            // 数字按键按钮
            ApplyNumpadButtonStyles();
        }

        /// <summary>
        /// 应用数字按键现代化样式
        /// </summary>
        private void ApplyNumpadButtonStyles()
        {
            // 数字按钮
            for (int i = 0; i <= 9; i++)
            {
                var btnName = $"btnNum{i}";
                var numButton = this.Controls.Find(btnName, true).FirstOrDefault() as Button;
                if (numButton != null)
                {
                    ModernStyleManager.ApplyModernButtonStyle(numButton, ModernStyleManager.ButtonStyle.Outline);
                }
            }

            // 特殊按钮
            var btnClear = this.Controls.Find("btnClear", true).FirstOrDefault() as Button;
            if (btnClear != null)
                ModernStyleManager.ApplyModernButtonStyle(btnClear, ModernStyleManager.ButtonStyle.Warning);

            var btnDelete = this.Controls.Find("btnDelete", true).FirstOrDefault() as Button;
            if (btnDelete != null)
                ModernStyleManager.ApplyModernButtonStyle(btnDelete, ModernStyleManager.ButtonStyle.Error);
        }

        /// <summary>
        /// 设置触控友好的界面
        /// </summary>
        private void SetupTouchFriendlyUI()
        {
            // 设置窗体全屏显示（可选）
            // this.WindowState = FormWindowState.Maximized;

            // 设置触控友好的控件样式
            foreach (Control control in GetAllControls(this))
            {
                if (control is Button button)
                {
                    // Designer中已设置了触控友好的按钮样式
                    // 悬停效果已禁用
                    // SetupButtonHoverEffects(button); // 注释掉悬停效果
                }
                else if (control is ListView listView)
                {
                    // 优化列表视图的触控体验
                    listView.Font = new System.Drawing.Font("微软雅黑", 14F);
                    listView.View = View.Details;
                    listView.FullRowSelect = true;
                    listView.GridLines = true;
                    listView.CheckBoxes = true;
                    listView.HeaderStyle = ColumnHeaderStyle.Nonclickable;

                    // 设置更大的行高以便触控
                    listView.OwnerDraw = true;
                    listView.DrawItem += ListView_DrawItem;
                    listView.DrawSubItem += ListView_DrawSubItem;
                    listView.DrawColumnHeader += ListView_DrawColumnHeader;
                }
                else if (control is Label label && label.Name.StartsWith("lblStep"))
                {
                    // 步骤标签样式优化
                    label.BackColor = System.Drawing.Color.FromArgb(248, 249, 250);
                    label.ForeColor = System.Drawing.Color.FromArgb(52, 144, 220);
                }
                else if (control is Label label2 && (label2.Name.Contains("PhoneNumber") || label2.Name.Contains("VerificationCode")))
                {
                    // 输入提示标签优化
                    label2.Font = new System.Drawing.Font("微软雅黑", 16F, System.Drawing.FontStyle.Bold);
                    label2.ForeColor = System.Drawing.Color.FromArgb(73, 80, 87);
                }
            }

            // 添加面板圆角效果
            AddPanelShadowEffect();
        }

        /// <summary>
        /// 设置按钮悬停效果
        /// </summary>
        private void SetupButtonHoverEffects(Button button)
        {
            var originalColor = button.BackColor;

            button.MouseEnter += (s, e) => {
                button.BackColor = AdjustBrightness(originalColor, 0.15f);
                button.Cursor = Cursors.Hand;
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = originalColor;
                button.Cursor = Cursors.Default;
            };
        }

        /// <summary>
        /// 添加面板圆角效果
        /// </summary>
        private void AddPanelShadowEffect()
        {
            // 为步骤面板添加圆角效果
            panelStep1.Paint += Panel_Paint;
            panelStep2.Paint += Panel_Paint;
            panelStep3.Paint += Panel_Paint;
        }

        /// <summary>
        /// 面板绘制事件 - 添加圆角效果
        /// </summary>
        private void Panel_Paint(object sender, PaintEventArgs e)
        {
            Panel panel = sender as Panel;
            if (panel != null)
            {
                // 绘制圆角矩形背景
                using (var path = GetRoundedRectanglePath(panel.ClientRectangle, 8))
                {
                    panel.Region = new System.Drawing.Region(path);
                }
            }
        }

        /// <summary>
        /// 获取圆角矩形路径
        /// </summary>
        private System.Drawing.Drawing2D.GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new System.Drawing.Drawing2D.GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        /// <summary>
        /// ListView自定义绘制 - 增加行高
        /// </summary>
        private void ListView_DrawItem(object sender, DrawListViewItemEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// ListView自定义绘制子项
        /// </summary>
        private void ListView_DrawSubItem(object sender, DrawListViewSubItemEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// ListView自定义绘制列头
        /// </summary>
        private void ListView_DrawColumnHeader(object sender, DrawListViewColumnHeaderEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// 设置按钮主题颜色
        /// </summary>
        private void SetButtonTheme(Button button)
        {
            // 主要操作按钮 - 蓝色主题
            if (button.Name.Contains("Send") || button.Name.Contains("Verify") || button.Name.Contains("Print"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(52, 144, 220);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 次要操作按钮 - 绿色主题
            else if (button.Name.Contains("Preview") || button.Name.Contains("Select"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(40, 167, 69);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 返回/取消按钮 - 灰色主题
            else if (button.Name.Contains("Back") || button.Name.Contains("Cancel"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(108, 117, 125);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 默认按钮样式
            else
            {
                button.BackColor = System.Drawing.Color.FromArgb(248, 249, 250);
                button.ForeColor = System.Drawing.Color.FromArgb(33, 37, 41);
                button.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(206, 212, 218);
                button.FlatAppearance.BorderSize = 1;
            }

            // 禁用悬停效果 - 不设置MouseOverBackColor
            // button.FlatAppearance.MouseOverBackColor = AdjustBrightness(button.BackColor, 0.1f);
        }

        /// <summary>
        /// 调整颜色亮度
        /// </summary>
        private System.Drawing.Color AdjustBrightness(System.Drawing.Color color, float factor)
        {
            int r = Math.Min(255, Math.Max(0, (int)(color.R * (1 + factor))));
            int g = Math.Min(255, Math.Max(0, (int)(color.G * (1 + factor))));
            int b = Math.Min(255, Math.Max(0, (int)(color.B * (1 + factor))));
            return System.Drawing.Color.FromArgb(r, g, b);
        }

        /// <summary>
        /// 获取所有子控件
        /// </summary>
        private IEnumerable<Control> GetAllControls(Control container)
        {
            var controls = container.Controls.Cast<Control>();
            return controls.SelectMany(ctrl => GetAllControls(ctrl)).Concat(controls);
        }

        #region 卡片点击事件处理

        /// <summary>
        /// 手机号验证卡片点击事件
        /// </summary>
        private void cardPhoneInput_Click(object sender, EventArgs e)
        {
            if (cardPhoneInput != null && !cardPhoneInput.Enabled) return;

            // 显示第一步面板
            ShowStep(1);
        }

        /// <summary>
        /// 验证码确认卡片点击事件
        /// </summary>
        private void cardVerifyCode_Click(object sender, EventArgs e)
        {
            if (cardVerifyCode != null && !cardVerifyCode.Enabled) return;

            ShowStep(2);
        }

        /// <summary>
        /// 选择发运单卡片点击事件
        /// </summary>
        private void cardSelectNotes_Click(object sender, EventArgs e)
        {
            if (cardSelectNotes != null && !cardSelectNotes.Enabled) return;

            ShowStep(3);
        }

        /// <summary>
        /// 打印预览卡片点击事件
        /// </summary>
        private void cardPrintPreview_Click(object sender, EventArgs e)
        {
            if (cardPrintPreview != null && !cardPrintPreview.Enabled) return;

            btnPreview_Click(sender, e);
        }

        /// <summary>
        /// 打印发运单卡片点击事件
        /// </summary>
        private void cardPrintNotes_Click(object sender, EventArgs e)
        {
            if (cardPrintNotes != null && !cardPrintNotes.Enabled) return;

            btnPrint_Click(sender, e);
        }

        /// <summary>
        /// 帮助卡片点击事件
        /// </summary>
        private void cardHelp_Click(object sender, EventArgs e)
        {
            ShowHelpDialog();
        }

        #endregion

        #region 面板显示方法

        /// <summary>
        /// 返回主界面按钮点击事件
        /// </summary>
        private void btnBackToMain_Click(object sender, EventArgs e)
        {
            ShowMainInterface();
        }

        /// <summary>
        /// 显示帮助对话框
        /// </summary>
        private void ShowHelpDialog()
        {
            string helpText = @"发运单打印系统使用说明：

1. 📱 手机号验证
   - 输入您的手机号码
   - 点击发送验证码

2. 🔐 验证码确认
   - 输入收到的短信验证码
   - 点击验证并继续

3. 📋 选择发运单
   - 从列表中选择要打印的发运单
   - 只能选择一张发运单

4. 👁️ 打印预览
   - 预览发运单内容

5. 🖨️ 打印发运单
   - 确认打印选中的发运单

如有问题，请联系管理员。";

            ModernMessageBox.Show(this, helpText, "操作帮助", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        #region 数字按键面板

        /// <summary>
        /// 初始化数字按键面板
        /// </summary>
        private void InitializeNumPads()
        {
            // 初始化步骤1的数字按键面板
            InitializeNumPad(panelNumPad1, numButtons1, btnClear1, btnBackspace1, txtPhoneNumber);

            // 初始化步骤2的数字按键面板
            InitializeNumPad(panelNumPad2, numButtons2, btnClear2, btnBackspace2, txtVerificationCode);
        }

        /// <summary>
        /// 初始化单个数字按键面板
        /// </summary>
        private void InitializeNumPad(Panel panel, Button[] numButtons, Button clearBtn, Button backspaceBtn, TextBox targetTextBox)
        {
            if (panel == null) return;

            panel.BackColor = Color.FromArgb(245, 245, 245);
            panel.Size = new Size(300, 400);
            panel.Location = new Point(50, 250);

            // 创建数字按钮 (0-9)
            for (int i = 0; i < 10; i++)
            {
                numButtons[i] = new Button();
                numButtons[i].Text = i.ToString();
                numButtons[i].Font = new Font("微软雅黑", 18F, FontStyle.Bold);
                numButtons[i].Size = new Size(80, 80);
                numButtons[i].BackColor = Color.White;
                numButtons[i].ForeColor = Color.Black;
                numButtons[i].FlatStyle = FlatStyle.Flat;
                numButtons[i].FlatAppearance.BorderColor = Color.Gray;
                numButtons[i].FlatAppearance.BorderSize = 1;
                numButtons[i].UseVisualStyleBackColor = false;
                numButtons[i].Cursor = Cursors.Hand;

                // 设置按钮位置 (3x4布局，最后一行是 清除、0、退格)
                int row, col;
                if (i == 0)
                {
                    row = 3; col = 1; // 0在最后一行中间
                }
                else
                {
                    row = (i - 1) / 3;
                    col = (i - 1) % 3;
                }

                numButtons[i].Location = new Point(10 + col * 90, 10 + row * 90);

                // 添加点击事件
                int digit = i; // 捕获循环变量
                numButtons[i].Click += (s, e) => {
                    if (targetTextBox != null && !targetTextBox.ReadOnly)
                    {
                        targetTextBox.Text += digit.ToString();
                    }
                };

                panel.Controls.Add(numButtons[i]);
            }

            // 创建清除按钮
            clearBtn.Text = "清除";
            clearBtn.Font = new Font("微软雅黑", 14F, FontStyle.Bold);
            clearBtn.Size = new Size(80, 80);
            clearBtn.Location = new Point(10, 280); // 最后一行左边
            clearBtn.BackColor = Color.FromArgb(220, 53, 69);
            clearBtn.ForeColor = Color.White;
            clearBtn.FlatStyle = FlatStyle.Flat;
            clearBtn.FlatAppearance.BorderSize = 0;
            clearBtn.UseVisualStyleBackColor = false;
            clearBtn.Cursor = Cursors.Hand;
            clearBtn.Click += (s, e) => {
                if (targetTextBox != null && !targetTextBox.ReadOnly)
                {
                    targetTextBox.Text = "";
                }
            };
            panel.Controls.Add(clearBtn);

            // 创建退格按钮
            backspaceBtn.Text = "⌫";
            backspaceBtn.Font = new Font("微软雅黑", 18F, FontStyle.Bold);
            backspaceBtn.Size = new Size(80, 80);
            backspaceBtn.Location = new Point(190, 280); // 最后一行右边
            backspaceBtn.BackColor = Color.FromArgb(108, 117, 125);
            backspaceBtn.ForeColor = Color.White;
            backspaceBtn.FlatStyle = FlatStyle.Flat;
            backspaceBtn.FlatAppearance.BorderSize = 0;
            backspaceBtn.UseVisualStyleBackColor = false;
            backspaceBtn.Cursor = Cursors.Hand;
            backspaceBtn.Click += (s, e) => {
                if (targetTextBox != null && !targetTextBox.ReadOnly && targetTextBox.Text.Length > 0)
                {
                    targetTextBox.Text = targetTextBox.Text.Substring(0, targetTextBox.Text.Length - 1);
                }
            };
            panel.Controls.Add(backspaceBtn);
        }

        #endregion

        /// <summary>
        /// 显示指定步骤
        /// </summary>
        private void ShowStep(int step)
        {
            _currentStep = step;

            // 隐藏所有面板
            if (panelStep1 != null) panelStep1.Visible = false;
            if (panelStep2 != null) panelStep2.Visible = false;
            if (panelStep3 != null) panelStep3.Visible = false;
            if (panelPhoneInput != null) panelPhoneInput.Visible = false;
            if (panelVerification != null) panelVerification.Visible = false;
            if (panelNotesList != null) panelNotesList.Visible = false;
            if (panelCardContainer != null) panelCardContainer.Visible = false;

            // 显示当前步骤
            switch (step)
            {
                case 1:
                    if (panelStep1 != null)
                    {
                        panelStep1.Visible = true;
                        AdjustStepPanelLayout(panelStep1);
                        if (txtPhoneNumber != null) txtPhoneNumber.Focus();

                        // 检查是否有倒计时在进行，如果有则保持倒计时状态
                        if (_countdownTimer != null && _countdownTimer.Enabled)
                        {
                            UpdateStatus($"验证码已发送，{_countdownSeconds}秒后可重新发送");
                        }
                        else
                        {
                            UpdateStatus("请输入手机号码");
                        }
                    }
                    else
                    {
                        UpdateStatus("请输入手机号码");
                    }
                    break;
                case 2:
                    if (panelStep2 != null)
                    {
                        panelStep2.Visible = true;
                        AdjustStepPanelLayout(panelStep2);
                        if (txtVerificationCode != null) txtVerificationCode.Focus();
                    }
                    UpdateStatus("请输入验证码");
                    break;
                case 3:
                    if (panelStep3 != null)
                    {
                        panelStep3.Visible = true;
                        AdjustStepPanelLayout(panelStep3);
                    }
                    UpdateStatus("请选择要打印的发运单");
                    break;
            }
        }

        /// <summary>
        /// 发送验证码按钮点击事件
        /// </summary>
        private async void btnSendCode_Click(object sender, EventArgs e)
        {
            try
            {
                string phoneNumber = txtPhoneNumber.Text.Trim();
                
                // 验证手机号格式
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    ModernMessageBox.Show(this, "请输入正确的手机号码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPhoneNumber.Focus();
                    return;
                }
                
                _currentPhoneNumber = phoneNumber;
                
                // 禁用按钮，显示加载状态
                btnSendCode.Enabled = false;
                btnSendCode.Text = "检查中...";
                UpdateStatus("正在检查手机号...");

                // 首先调用getPostBill检查手机号是否存在发运数据
                bool hasPostBill = await Task.Run(() => _postBillService.CheckPostBillExists(phoneNumber));

                if (!hasPostBill)
                {
                    ModernMessageBox.Show(this, "该手机号没有相关的发运数据，请联系管理人员维护司机手机号！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    btnSendCode.Enabled = true;
                    btnSendCode.Text = "发送验证码";
                    UpdateStatus("请输入手机号码");
                    txtPhoneNumber.Focus();
                    return;
                }

                // 如果存在发运数据，则发送验证码
                btnSendCode.Text = "发送中...";
                UpdateStatus("正在发送验证码...");

                // 调用addkaptcha发送验证码
                bool success = await Task.Run(() => _postBillService.SendVerificationCode(phoneNumber));
                
                if (success)
                {
                    ToastNotification.ShowSuccess("验证码已发送到您的手机，请查收！");

                    // 开始60秒倒计时
                    StartCountdown(60);
                    UpdateStatus("验证码已发送，60秒后可重新发送");

                    // 直接显示第二步
                    ShowStep(2);
                }
                else
                {
                    ToastNotification.ShowError("验证码发送失败，请重试！");
                    // 发送失败时恢复按钮状态
                    btnSendCode.Enabled = true;
                    btnSendCode.Text = _originalSendButtonText;
                    UpdateStatus("");
                }
            }
            catch (Exception ex)
            {
                ModernMessageBox.Show(this, $"发送验证码时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // 发生异常时恢复按钮状态
                btnSendCode.Enabled = true;
                btnSendCode.Text = _originalSendButtonText;
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 验证验证码按钮点击事件
        /// </summary>
        private async void btnVerifyCode_Click(object sender, EventArgs e)
        {
            try
            {
                string verificationCode = txtVerificationCode.Text.Trim();
                
                if (string.IsNullOrEmpty(verificationCode))
                {
                    ModernMessageBox.Show(this, "请输入验证码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtVerificationCode.Focus();
                    return;
                }
                
                // 禁用按钮，显示加载状态
                btnVerifyCode.Enabled = false;
                btnVerifyCode.Text = "验证中...";
                UpdateStatus("正在验证验证码...");
                
                // 验证验证码
                bool success = await Task.Run(() => _postBillService.VerifyCode(verificationCode));
                
                if (success)
                {
                    // 验证成功，直接查询发运单并跳转到步骤3
                    await LoadShippingNotes();
                }
                else
                {
                    ToastNotification.ShowError("验证码错误，请重新输入！");
                    txtVerificationCode.SelectAll();
                    txtVerificationCode.Focus();
                }
            }
            catch (Exception ex)
            {
                ModernMessageBox.Show(this, $"验证验证码时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnVerifyCode.Enabled = true;
                btnVerifyCode.Text = "验证";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 加载发运单列表
        /// </summary>
        private async Task LoadShippingNotes()
        {
            try
            {
                UpdateStatus("正在查询发运单...");

                // 查询发运单 - 使用PostBillService获取发运单明细
                _shippingNotes = await Task.Run(() => _postBillService.GetShippingNotesByPhone(_currentPhoneNumber));

                if (_shippingNotes == null || _shippingNotes.Count == 0)
                {
                    ToastNotification.ShowWarning("未找到相关的发运单！");
                    ShowMainInterface();
                    return;
                }

                // 填充发运单列表
                PopulateShippingNotesList();

                // 显示第三步
                ShowStep(3);
            }
            catch (Exception ex)
            {
                ModernMessageBox.Show(this, $"查询发运单时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ShowStep(1);
            }
        }

        /// <summary>
        /// 验证手机号格式
        /// </summary>
        private bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber) || phoneNumber.Length != 11)
                return false;
                
            return phoneNumber.All(char.IsDigit) && phoneNumber.StartsWith("1");
        }

        /// <summary>
        /// 填充发运单列表
        /// </summary>
        private void PopulateShippingNotesList()
        {
            listViewShippingNotes.Items.Clear();

            foreach (var note in _shippingNotes)
            {
                ListViewItem item = new ListViewItem(note.ShippingNoteNumber);
                item.SubItems.Add(note.CustomerUnit);
                item.SubItems.Add(note.ShippingWarehouse);
                item.SubItems.Add(note.DeliveryMethod);
                item.SubItems.Add(note.ShippingDate.ToString("yyyy-MM-dd"));
                item.Tag = note;

                listViewShippingNotes.Items.Add(item);
            }

            lblShippingNotesCount.Text = $"共找到 {_shippingNotes.Count} 张发运单";
        }

        /// <summary>
        /// ListView项目选中状态改变事件 - 实现单选功能
        /// </summary>
        private void listViewShippingNotes_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            // 如果当前项目被选中，取消其他所有项目的选中状态
            if (e.NewValue == CheckState.Checked)
            {
                // 延迟执行，避免在ItemCheck事件中直接修改其他项目状态
                this.BeginInvoke(new Action(() =>
                {
                    for (int i = 0; i < listViewShippingNotes.Items.Count; i++)
                    {
                        if (i != e.Index)
                        {
                            listViewShippingNotes.Items[i].Checked = false;
                        }
                    }
                }));
            }
        }

        /// <summary>
        /// ListView鼠标点击事件 - 点击整行也能勾选
        /// </summary>
        private void listViewShippingNotes_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                ListViewHitTestInfo hit = listViewShippingNotes.HitTest(e.Location);
                if (hit.Item != null)
                {
                    // 获取点击的项目
                    ListViewItem clickedItem = hit.Item;

                    // 检查是否点击在复选框区域
                    Rectangle checkBoxBounds = new Rectangle(0, clickedItem.Bounds.Y, 16, clickedItem.Bounds.Height);

                    // 如果不是点击复选框区域，则切换选中状态
                    if (!checkBoxBounds.Contains(e.Location))
                    {
                        // 切换当前项目的选中状态
                        clickedItem.Checked = !clickedItem.Checked;
                    }
                }
            }
        }

        /// <summary>
        /// 打印预览按钮点击事件
        /// </summary>
        private async void btnPreview_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取选中的发运单
                var selectedNotes = GetSelectedShippingNotes();

                if (selectedNotes.Count == 0)
                {
                    ToastNotification.ShowWarning("请选择一张发运单进行预览！");
                    return;
                }

                // 预览选中的发运单
                var note = selectedNotes[0];

                // 禁用按钮，显示加载状态
                btnPreview.Enabled = false;
                btnPreview.Text = "预览中...";
                UpdateStatus("正在生成打印预览...");

                // 直接使用已获取的完整发运单数据（包含所有明细项）
                await Task.Run(() => _printService.ShowPrintPreview(note));
            }
            catch (Exception ex)
            {
                ModernMessageBox.Show(this, $"显示打印预览时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnPreview.Enabled = true;
                btnPreview.Text = "打印预览";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private async void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取选中的发运单
                var selectedNotes = GetSelectedShippingNotes();

                if (selectedNotes.Count == 0)
                {
                    ToastNotification.ShowWarning("请选择一张发运单进行打印！");
                    return;
                }

                // 确认打印
                string message = $"确定要打印选中的发运单吗？";
                if (ModernMessageBox.Show(this, message, "确认打印", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                {
                    //ToastNotification.ShowWarning("222！");
                    return;
                }
                //ToastNotification.ShowWarning("1111！");
                // 禁用按钮，显示加载状态
                btnPrint.Enabled = false;
                btnPrint.Text = "打印中...";
                UpdateStatus("正在打印发运单...");

                // 打印选中的发运单
                var note = selectedNotes[0];
                try
                {
                    // 直接使用已获取的完整发运单数据（包含所有明细项）
                    await Task.Run(() => _printService.PrintShippingNoteDirect(note));

                    ToastNotification.ShowSuccess("打印完成！");
                }
                catch (Exception ex)
                {
                    ModernMessageBox.Show(this, $"打印发运单 {note.ShippingNoteNumber} 时发生错误：{ex.Message}",
                        "打印错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // 打印完成后返回主界面
                ShowMainInterface();
                ResetForm();
            }
            catch (Exception ex)
            {
                ModernMessageBox.Show(this, $"打印过程中发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnPrint.Enabled = true;
                btnPrint.Text = "打印发运单";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 获取选中的发运单
        /// </summary>
        private List<ShippingNote> GetSelectedShippingNotes()
        {
            var selectedNotes = new List<ShippingNote>();

            foreach (ListViewItem item in listViewShippingNotes.Items)
            {
                if (item.Checked && item.Tag is ShippingNote note)
                {
                    selectedNotes.Add(note);
                }
            }

            return selectedNotes;
        }

        /// <summary>
        /// 返回按钮点击事件（返回首页）
        /// </summary>
        private void btnBack_Click(object sender, EventArgs e)
        {
            ShowMainInterface();
            ResetForm();
        }

        /// <summary>
        /// 重置表单
        /// </summary>
        private void ResetForm()
        {
            txtPhoneNumber.Text = "";
            txtVerificationCode.Text = "";
            _currentPhoneNumber = "";
            _shippingNotes.Clear();
            listViewShippingNotes.Items.Clear();
            lblShippingNotesCount.Text = "共找到 0 张发运单";
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatus(string message)
        {
            lblStatus.Text = message;
            Application.DoEvents();
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            // 调整布局以适应当前窗体大小
            AdjustLayout();
        }

        /// <summary>
        /// 窗体大小变化事件
        /// </summary>
        private void MainForm_Resize(object sender, EventArgs e)
        {
            // 调整布局以适应新的窗体大小
            AdjustLayout();
        }

        /// <summary>
        /// 调整布局以适应窗体大小
        /// </summary>
        private void AdjustLayout()
        {
            if (this.WindowState == FormWindowState.Minimized)
                return;

            // 调整标题栏
            AdjustTitleBar();

            // 调整卡片容器布局
            AdjustCardContainer();

            // 调整状态栏
            AdjustStatusBar();
        }

        /// <summary>
        /// 调整标题栏布局
        /// </summary>
        private void AdjustTitleBar()
        {
            if (lblTitle != null)
            {
                lblTitle.Size = new Size(this.ClientSize.Width, 80);
                lblTitle.Location = new Point(0, 0);
                lblTitle.TextAlign = ContentAlignment.MiddleCenter; // 居中显示
                lblTitle.Padding = new Padding(0); // 移除左边距
            }

            if (lblTime != null)
            {
                lblTime.Size = new Size(200, 80);
                lblTime.Location = new Point(this.ClientSize.Width - 200, 0);
                lblTime.TextAlign = ContentAlignment.MiddleCenter;
            }
        }

        /// <summary>
        /// 调整卡片容器布局
        /// </summary>
        private void AdjustCardContainer()
        {
            if (panelCardContainer != null)
            {
                // 计算卡片容器的位置和大小
                int margin = 40;
                int titleHeight = 80;
                int statusHeight = 50;

                int containerWidth = this.ClientSize.Width - (margin * 2);
                int containerHeight = this.ClientSize.Height - titleHeight - statusHeight - (margin * 2);

                panelCardContainer.Location = new Point(margin, titleHeight + margin);
                panelCardContainer.Size = new Size(containerWidth, containerHeight);

                // 调整卡片布局
                AdjustCardLayout();
            }
        }

        /// <summary>
        /// 调整卡片布局
        /// </summary>
        private void AdjustCardLayout()
        {
            if (panelCardContainer == null) return;

            // 卡片参数
            int cardWidth = 320;  // 增大卡片尺寸
            int cardHeight = 200;
            int cardSpacing = 40;

            // 检查是否只显示手机输入卡片
            bool onlyPhoneInput = cardPhoneInput != null && cardPhoneInput.Visible &&
                                 (cardVerifyCode == null || !cardVerifyCode.Visible) &&
                                 (cardSelectNotes == null || !cardSelectNotes.Visible) &&
                                 (cardPrintPreview == null || !cardPrintPreview.Visible) &&
                                 (cardPrintNotes == null || !cardPrintNotes.Visible) &&
                                 (cardHelp == null || !cardHelp.Visible);

            if (onlyPhoneInput)
            {
                // 单个卡片居中显示
                if (cardPhoneInput != null)
                {
                    int centerX = (panelCardContainer.Width - cardWidth) / 2;
                    int centerY = (panelCardContainer.Height - cardHeight) / 2;
                    cardPhoneInput.Location = new Point(centerX, centerY);
                    cardPhoneInput.Size = new Size(cardWidth, cardHeight);
                }
            }
            else
            {
                // 多个卡片的布局（如果需要的话）
                int cardsPerRow = Math.Max(1, (panelCardContainer.Width + cardSpacing) / (cardWidth + cardSpacing));

                if (cardsPerRow >= 3)
                {
                    int totalCardsWidth = 3 * cardWidth + 2 * cardSpacing;
                    int startX = (panelCardContainer.Width - totalCardsWidth) / 2;

                    // 第一行卡片
                    if (cardPhoneInput != null && cardPhoneInput.Visible)
                    {
                        cardPhoneInput.Location = new Point(startX, cardSpacing);
                        cardPhoneInput.Size = new Size(cardWidth, cardHeight);
                    }

                    if (cardVerifyCode != null && cardVerifyCode.Visible)
                    {
                        cardVerifyCode.Location = new Point(startX + cardWidth + cardSpacing, cardSpacing);
                        cardVerifyCode.Size = new Size(cardWidth, cardHeight);
                    }

                    if (cardSelectNotes != null && cardSelectNotes.Visible)
                    {
                        cardSelectNotes.Location = new Point(startX + 2 * (cardWidth + cardSpacing), cardSpacing);
                        cardSelectNotes.Size = new Size(cardWidth, cardHeight);
                    }

                    // 第二行卡片
                    int secondRowY = cardSpacing + cardHeight + cardSpacing;

                    if (cardPrintPreview != null && cardPrintPreview.Visible)
                    {
                        cardPrintPreview.Location = new Point(startX, secondRowY);
                        cardPrintPreview.Size = new Size(cardWidth, cardHeight);
                    }

                    if (cardPrintNotes != null && cardPrintNotes.Visible)
                    {
                        cardPrintNotes.Location = new Point(startX + cardWidth + cardSpacing, secondRowY);
                        cardPrintNotes.Size = new Size(cardWidth, cardHeight);
                    }

                    if (cardHelp != null && cardHelp.Visible)
                    {
                        cardHelp.Location = new Point(startX + 2 * (cardWidth + cardSpacing), secondRowY);
                        cardHelp.Size = new Size(cardWidth, cardHeight);
                    }
                }
            }
        }

        /// <summary>
        /// 调整步骤面板布局
        /// </summary>
        private void AdjustStepPanelLayout(Panel stepPanel)
        {
            if (stepPanel == null) return;

            // 计算面板的位置和大小
            int margin = 40;
            int titleHeight = 80;
            int statusHeight = 50;

            int panelWidth = this.ClientSize.Width - (margin * 2);
            int panelHeight = this.ClientSize.Height - titleHeight - statusHeight - (margin * 2);

            stepPanel.Location = new Point(margin, titleHeight + margin);
            stepPanel.Size = new Size(panelWidth, panelHeight);

            // 调整面板内控件的布局
            AdjustStepPanelControls(stepPanel);
        }

        /// <summary>
        /// 调整步骤面板内控件的布局
        /// </summary>
        private void AdjustStepPanelControls(Panel stepPanel)
        {
            if (stepPanel == null) return;

            // 为每个步骤面板添加返回首页按钮
            AddBackToHomeButton(stepPanel);

            // 根据面板类型调整控件布局
            if (stepPanel == panelStep1)
            {
                AdjustStep1Layout();
            }
            else if (stepPanel == panelStep2)
            {
                AdjustStep2Layout();
            }
            else if (stepPanel == panelStep3)
            {
                AdjustStep3Layout();
            }
        }

        /// <summary>
        /// 为步骤面板添加返回首页按钮
        /// </summary>
        private void AddBackToHomeButton(Panel stepPanel)
        {
            // 查找是否已存在返回首页按钮
            Button backToHomeBtn = stepPanel.Controls.OfType<Button>()
                .FirstOrDefault(b => b.Name == "btnBackToHome");

            if (backToHomeBtn == null)
            {
                // 创建返回首页按钮
                backToHomeBtn = new Button
                {
                    Name = "btnBackToHome",
                    Text = "⬅️ 返回首页",
                    Font = new Font("微软雅黑", 14F, FontStyle.Bold),
                    BackColor = Color.FromArgb(108, 117, 125),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Size = new Size(140, 50),
                    Location = new Point(20, stepPanel.Height - 70),
                    TabIndex = 100
                };
                backToHomeBtn.FlatAppearance.BorderSize = 0;
                backToHomeBtn.Click += (s, e) => ShowMainInterface();

                stepPanel.Controls.Add(backToHomeBtn);
            }
            else
            {
                // 更新位置
                backToHomeBtn.Location = new Point(20, stepPanel.Height - 70);
            }
        }

        /// <summary>
        /// 调整步骤1布局（手机号输入）
        /// </summary>
        private void AdjustStep1Layout()
        {
            if (panelStep1 == null) return;

            int centerX = panelStep1.Width / 2;
            int centerY = panelStep1.Height / 2;

            // 调整标题位置
            if (lblStep1 != null)
            {
                lblStep1.Location = new Point(20, 20);
                lblStep1.Size = new Size(panelStep1.Width - 40, 40);
                lblStep1.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整提示标签位置
            if (lblPhoneNumber != null)
            {
                lblPhoneNumber.Location = new Point(centerX - 170, 80);
                lblPhoneNumber.Size = new Size(340, 30);
                lblPhoneNumber.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整输入框位置
            if (txtPhoneNumber != null)
            {
                txtPhoneNumber.Location = new Point(centerX - 170, 120);
                txtPhoneNumber.Size = new Size(340, 50);
            }

            // 调整按钮位置
            if (btnSendCode != null)
            {
                btnSendCode.Location = new Point(centerX - 120, 190);
                btnSendCode.Size = new Size(240, 80);
            }

            // 调整数字按键面板位置
            if (panelNumPad1 != null)
            {
                panelNumPad1.Location = new Point(centerX + 200, 80);
                panelNumPad1.Size = new Size(300, 400);
            }
        }

        /// <summary>
        /// 调整步骤2布局（验证码输入）
        /// </summary>
        private void AdjustStep2Layout()
        {
            if (panelStep2 == null) return;

            int centerX = panelStep2.Width / 2;
            int centerY = panelStep2.Height / 2;

            // 调整标题位置
            if (lblStep2 != null)
            {
                lblStep2.Location = new Point(20, 20);
                lblStep2.Size = new Size(panelStep2.Width - 40, 40);
                lblStep2.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整提示标签位置
            if (lblVerificationCode != null)
            {
                lblVerificationCode.Location = new Point(centerX - 170, 80);
                lblVerificationCode.Size = new Size(340, 30);
                lblVerificationCode.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整输入框位置
            if (txtVerificationCode != null)
            {
                txtVerificationCode.Location = new Point(centerX - 170, 120);
                txtVerificationCode.Size = new Size(340, 50);
            }

            // 调整按钮位置
            if (btnVerifyCode != null)
            {
                btnVerifyCode.Location = new Point(centerX - 120, 190);
                btnVerifyCode.Size = new Size(240, 80);
            }

            // 调整数字按键面板位置
            if (panelNumPad2 != null)
            {
                panelNumPad2.Location = new Point(centerX + 200, 80);
                panelNumPad2.Size = new Size(300, 400);
            }
        }

        /// <summary>
        /// 调整步骤3布局（发运单列表）
        /// </summary>
        private void AdjustStep3Layout()
        {
            if (panelStep3 == null) return;

            // 调整标题位置
            if (lblStep3 != null)
            {
                lblStep3.Location = new Point(20, 20);
                lblStep3.Size = new Size(panelStep3.Width - 40, 40);
                lblStep3.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整发运单数量标签位置
            if (lblShippingNotesCount != null)
            {
                lblShippingNotesCount.Location = new Point(20, 70);
                lblShippingNotesCount.Size = new Size(panelStep3.Width - 40, 30);
                lblShippingNotesCount.TextAlign = ContentAlignment.MiddleLeft;
            }

            // 调整列表视图位置 - 增加高度，因为移除了全选按钮
            if (listViewShippingNotes != null)
            {
                listViewShippingNotes.Location = new Point(20, 110);
                // 确保ListView有足够宽度显示所有列，最小宽度1120
                int listViewWidth = Math.Max(1120, panelStep3.Width - 40);
                listViewShippingNotes.Size = new Size(listViewWidth, panelStep3.Height - 200);
            }

            // 调整按钮位置 - 只有打印预览和打印发运单两个按钮
            int buttonY = panelStep3.Height - 90;
            int buttonSpacing = 30;
            int buttonWidth = 180;
            int buttonHeight = 70;

            if (btnPreview != null)
            {
                btnPreview.Location = new Point(panelStep3.Width - 2 * buttonWidth - buttonSpacing - 40, buttonY);
                btnPreview.Size = new Size(buttonWidth, buttonHeight);
            }

            if (btnPrint != null)
            {
                btnPrint.Location = new Point(panelStep3.Width - buttonWidth - 20, buttonY);
                btnPrint.Size = new Size(buttonWidth, buttonHeight);
            }
        }

        /// <summary>
        /// 调整状态栏布局
        /// </summary>
        private void AdjustStatusBar()
        {
            if (lblStatus != null)
            {
                lblStatus.Location = new Point(40, this.ClientSize.Height - 50);
                lblStatus.Size = new Size(this.ClientSize.Width - 80, 30);
                lblStatus.TextAlign = ContentAlignment.MiddleCenter;
            }
        }

        /// <summary>
        /// 窗体关闭时释放资源
        /// </summary>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _printService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
