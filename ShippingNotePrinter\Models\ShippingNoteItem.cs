namespace ShippingNotePrinter.Models
{
    /// <summary>
    /// 发运单明细项目模型
    /// </summary>
    public class ShippingNoteItem
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 品名规格 (mm)
        /// </summary>
        public string ProductSpecification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 数量 (ENTRIESASSISTQTY)
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 数 (ENTRIESQTY)
        /// </summary>
        public decimal Count { get; set; }

        /// <summary>
        /// 单重 (MATERIALGROSSWEIGHT)
        /// </summary>
        public decimal UnitWeight { get; set; }

        /// <summary>
        /// 重量123 (MATERIALGROSSWEIGHT123)
        /// </summary>
        public decimal Weight123 { get; set; }

        /// <summary>
        /// 标长/容量 (MATERIALHELPCODE)
        /// </summary>
        public string StandardLength { get; set; }

        /// <summary>
        /// 单体积 (MATERIALVOLUME)
        /// </summary>
        public decimal UnitVolume { get; set; }

        /// <summary>
        /// 总体积 (MATERIALVOLUME * ENTRIESQTY)
        /// </summary>
        public decimal TotalVolume { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 物料简称 (MATERIALSHORTNAME) - 用于分组显示
        /// </summary>
        public string MaterialShortName { get; set; }

        /// <summary>
        /// 物料名称1 (MATERIALNAME1) - 用于分页条件
        /// </summary>
        public string MaterialName1 { get; set; }

        /// <summary>
        /// 是否为分组标题行
        /// </summary>
        public bool IsGroupHeader { get; set; }

        /// <summary>
        /// 计算总体积
        /// </summary>
        public void CalculateTotalVolume()
        {
            TotalVolume = UnitVolume * Count;
        }
    }
}
