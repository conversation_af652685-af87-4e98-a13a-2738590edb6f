using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Windows.Forms;
using ShippingNotePrinter.Models;
using ZXing;
using ZXing.Common;
using BarcodeWriter = ZXing.Windows.Compatibility.BarcodeWriter;

namespace ShippingNotePrinter.Services
{
    /// <summary>
    /// 发运单打印服务
    /// </summary>
    public class PrintService
    {
        private readonly PrintDocument _printDocument;
        private ShippingNote _currentShippingNote;
        private int _currentPage;
        private int _previewPageIndex;
        private readonly Font _headerFont;
        private readonly Font _contentFont;
        private readonly Font _smallFont;
        private readonly Font _boldFont;

        public PrintService()
        {
            _printDocument = new PrintDocument();
            _printDocument.PrintPage += PrintDocument_PrintPage;
            
            // 初始化字体 - 进一步调大字体大小
            _headerFont = new Font("宋体", 20, FontStyle.Bold);
            _contentFont = new Font("宋体", 12);
            _smallFont = new Font("宋体", 11);
            _boldFont = new Font("宋体", 12, FontStyle.Bold);

            // 设置默认打印机
            string defaultPrinter = ConfigurationManager.AppSettings["DefaultPrinterName"];
            if (!string.IsNullOrEmpty(defaultPrinter))
            {
                _printDocument.PrinterSettings.PrinterName = defaultPrinter;
            }

            // 纸张大小配置：根据配置决定是否自动适配
            bool autoAdaptPaperSize = bool.Parse(ConfigurationManager.AppSettings["AutoAdaptPaperSize"] ?? "true");

            if (!autoAdaptPaperSize)
            {
                // 强制设置纸张大小
                int paperWidth = int.Parse(ConfigurationManager.AppSettings["ForcePaperWidth"] ?? "827");
                int paperHeight = int.Parse(ConfigurationManager.AppSettings["ForcePaperHeight"] ?? "1169");
                _printDocument.DefaultPageSettings.PaperSize = new PaperSize("Custom", paperWidth, paperHeight);
            }
            // 如果autoAdaptPaperSize=true，则使用打印机默认纸张大小，适合针式打印机

            // 设置相对较小的边距，适合各种纸张大小
            _printDocument.DefaultPageSettings.Margins = new Margins(15, 15, 30, 30); // 左右边距更小，上下保持适中
        }

        /// <summary>
        /// 打印发运单
        /// </summary>
        /// <param name="shippingNote">发运单数据</param>
        public void PrintShippingNote(ShippingNote shippingNote)
        {
            try
            {
                // 根据MATERIALNAME1进行分页处理
                var pagedShippingNotes = CreatePagedShippingNotes(new List<ShippingNote> { shippingNote });

                // 创建一个新的打印文档来处理多页打印
                PrintDocument multiPageDoc = new PrintDocument();

                // 应用纸张适配配置
                ApplyPaperSizeSettings(multiPageDoc);
                int currentPageIndex = 0;

                multiPageDoc.PrintPage += (sender, e) =>
                {
                    if (currentPageIndex < pagedShippingNotes.Count)
                    {
                        _currentShippingNote = pagedShippingNotes[currentPageIndex];
                        PrintDocument_PrintPage(sender, e);

                        currentPageIndex++;
                        e.HasMorePages = currentPageIndex < pagedShippingNotes.Count;
                    }
                };

                // 重置页面索引的事件处理
                multiPageDoc.BeginPrint += (sender, e) =>
                {
                    currentPageIndex = 0; // 每次开始打印时重置页面索引
                };

                // 设置打印机为单面打印
                multiPageDoc.DefaultPageSettings.PrinterSettings.Duplex = Duplex.Simplex;

                // 显示打印对话框
                PrintDialog printDialog = new PrintDialog();
                printDialog.Document = multiPageDoc;

                if (printDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    multiPageDoc.Print();
                }

                multiPageDoc.Dispose();
            }
            catch (Exception ex)
            {
                throw new Exception($"打印发运单失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 显示打印预览
        /// </summary>
        /// <param name="shippingNote">发运单数据</param>
        public void ShowPrintPreview(ShippingNote shippingNote)
        {
            try
            {
                // 根据MATERIALNAME1进行分页处理
                var pagedShippingNotes = CreatePagedShippingNotes(new List<ShippingNote> { shippingNote });

                // 创建一个新的打印文档来处理多页预览
                PrintDocument multiPageDoc = new PrintDocument();

                // 应用纸张适配配置
                ApplyPaperSizeSettings(multiPageDoc);
                int currentPageIndex = 0;

                multiPageDoc.PrintPage += (sender, e) =>
                {
                    if (currentPageIndex < pagedShippingNotes.Count)
                    {
                        _currentShippingNote = pagedShippingNotes[currentPageIndex];
                        PrintDocument_PrintPage(sender, e);

                        currentPageIndex++;
                        e.HasMorePages = currentPageIndex < pagedShippingNotes.Count;
                    }
                };

                // 重置页面索引的事件处理
                multiPageDoc.BeginPrint += (sender, e) =>
                {
                    currentPageIndex = 0; // 每次开始打印时重置页面索引
                };

                // 设置打印机为单面打印
                multiPageDoc.DefaultPageSettings.PrinterSettings.Duplex = Duplex.Simplex;

                // 显示打印预览对话框
                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                previewDialog.Document = multiPageDoc;
                previewDialog.Width = 1000;
                previewDialog.Height = 700;
                previewDialog.ShowDialog();

                multiPageDoc.Dispose();
            }
            catch (Exception ex)
            {
                throw new Exception($"显示打印预览失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 直接打印发运单（不显示对话框）
        /// </summary>
        /// <param name="shippingNote">发运单数据</param>
        public void PrintShippingNoteDirect(ShippingNote shippingNote)
        {
            try
            {
                // 根据MATERIALNAME1进行分页处理
                var pagedShippingNotes = CreatePagedShippingNotes(new List<ShippingNote> { shippingNote });

                // 创建一个新的打印文档来处理多页打印
                PrintDocument multiPageDoc = new PrintDocument();

                // 应用纸张适配配置
                ApplyPaperSizeSettings(multiPageDoc);
                int currentPageIndex = 0;

                multiPageDoc.PrintPage += (sender, e) =>
                {
                    if (currentPageIndex < pagedShippingNotes.Count)
                    {
                        _currentShippingNote = pagedShippingNotes[currentPageIndex];
                        PrintDocument_PrintPage(sender, e);

                        currentPageIndex++;
                        e.HasMorePages = currentPageIndex < pagedShippingNotes.Count;
                    }
                };

                // 重置页面索引的事件处理
                multiPageDoc.BeginPrint += (sender, e) =>
                {
                    currentPageIndex = 0; // 每次开始打印时重置页面索引
                };

                // 设置打印机为单面打印
                multiPageDoc.DefaultPageSettings.PrinterSettings.Duplex = Duplex.Simplex;

                // 直接打印，不显示对话框
                multiPageDoc.Print();

                multiPageDoc.Dispose();
            }
            catch (Exception ex)
            {
                throw new Exception($"直接打印发运单失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 打印页面事件处理
        /// </summary>
        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            if (_currentShippingNote == null) return;

            Graphics g = e.Graphics;
            float yPos = e.MarginBounds.Top;
            float leftMargin = e.MarginBounds.Left;
            float rightMargin = e.MarginBounds.Right;
            float pageWidth = e.MarginBounds.Width;

            // 针式打印机适配：动态调整布局以适应不同纸张大小
            // 记录实际纸张信息用于调试
            float actualPaperWidth = e.PageBounds.Width;
            float actualPaperHeight = e.PageBounds.Height;

            // 绘制条形码区域
            DrawBarcode(g, leftMargin, yPos, pageWidth);
            yPos += 45; // 增加条形码和标题之间的间距

            // 绘制标题
            DrawTitle(g, leftMargin, yPos, pageWidth);
            yPos += 55; // 增加标题和基本信息之间的间距

            // 绘制基本信息
            yPos = DrawBasicInfo(g, leftMargin, yPos, pageWidth);
            yPos += 25; // 增加基本信息和表格之间的间距

            // 绘制明细表格
            yPos = DrawItemsTable(g, leftMargin, yPos, pageWidth);
            yPos += 35; // 增加表格和底部信息之间的间距

            // 绘制底部信息
            DrawFooterInfo(g, leftMargin, yPos, pageWidth);

            // 检查是否需要更多页面
            e.HasMorePages = false;
        }

        /// <summary>
        /// 绘制条形码和二维码
        /// </summary>
        private void DrawBarcode(Graphics g, float x, float y, float width)
        {
            // 使用SON开头的订单号作为条形码和二维码内容
            string barcodeContent = _currentShippingNote.BarcodeContent ?? _currentShippingNote.OrderNumber ?? "SON012508040207";

            // 绘制Code128条形码
            DrawZXingBarcode(g, x, y, 200, 30, barcodeContent);

            // 绘制条形码下方的文字
            g.DrawString(barcodeContent, _smallFont, Brushes.Black, x, y + 32);

            // 右侧区域布局调整 - 为文字预留足够空间
            float rightAreaWidth = 250; // 右侧区域总宽度
            float rightAreaX = x + width - rightAreaWidth;

            // 绘制二维码
            float qrSize = 80;
            float qrX = rightAreaX;
            float qrY = y;
            DrawZXingQRCode(g, qrX, qrY, qrSize, qrSize, barcodeContent);

            // 在二维码右侧绘制文字信息，确保不重叠
            float textX = qrX + qrSize + 10; // 二维码右侧10像素间距
            g.DrawString($"第{_currentShippingNote.CurrentPage}页，共{_currentShippingNote.TotalPages}页", _smallFont, Brushes.Black, textX, qrY);
            g.DrawString($"NO: {_currentShippingNote.PostRequisitionNumber}", _smallFont, Brushes.Black, textX, qrY + 15);
            g.DrawString($"订单号: {_currentShippingNote.OrderNumber ?? barcodeContent}", _smallFont, Brushes.Black, textX, qrY + 30);
        }

        /// <summary>
        /// 使用ZXing生成Code128条形码
        /// </summary>
        private void DrawZXingBarcode(Graphics g, float x, float y, float width, float height, string content)
        {
            try
            {
                var writer = new BarcodeWriter
                {
                    Format = BarcodeFormat.CODE_128,
                    Options = new EncodingOptions
                    {
                        Width = (int)width,
                        Height = (int)height,
                        Margin = 0
                    }
                };

                using (var bitmap = writer.Write(content))
                {
                    g.DrawImage(bitmap, x, y, width, height);
                }
            }
            catch (Exception ex)
            {
                // 如果生成失败，绘制错误信息
                g.DrawString($"条形码生成失败: {ex.Message}", _smallFont, Brushes.Red, x, y);
            }
        }

        /// <summary>
        /// 使用ZXing生成QR二维码
        /// </summary>
        private void DrawZXingQRCode(Graphics g, float x, float y, float width, float height, string content)
        {
            try
            {
                var writer = new BarcodeWriter
                {
                    Format = BarcodeFormat.QR_CODE,
                    Options = new EncodingOptions
                    {
                        Width = (int)width,
                        Height = (int)height,
                        Margin = 1
                    }
                };

                using (var bitmap = writer.Write(content))
                {
                    g.DrawImage(bitmap, x, y, width, height);
                }
            }
            catch (Exception ex)
            {
                // 如果生成失败，绘制错误信息
                g.DrawString($"二维码生成失败: {ex.Message}", _smallFont, Brushes.Red, x, y);
            }
        }

        // 已移除自定义条形码生成方法，现在使用ZXing.Net库

        // 已移除自定义二维码生成方法，现在使用ZXing.Net库

        // 已移除所有自定义二维码辅助方法，现在使用ZXing.Net库

        // 已移除自定义矩阵生成方法，现在使用ZXing.Net库

        /// <summary>
        /// 计算行高度（考虑文字换行）
        /// </summary>
        private float CalculateRowHeight(Graphics g, string[] values, float[] columnWidths, float[] columnX, Font font)
        {
            float maxHeight = 20; // 最小行高

            for (int i = 0; i < values.Length && i < columnWidths.Length; i++)
            {
                if (string.IsNullOrEmpty(values[i])) continue;

                float columnWidth = columnWidths[i] - 4; // 减去左右边距
                SizeF textSize = g.MeasureString(values[i], font, (int)columnWidth);

                if (textSize.Height > maxHeight)
                {
                    maxHeight = textSize.Height + 4; // 加上上下边距
                }
            }

            return maxHeight;
        }

        /// <summary>
        /// 在单元格中绘制文字（支持自动换行）
        /// </summary>
        private void DrawTextInCell(Graphics g, string text, Rectangle cellRect, Font font, Brush brush)
        {
            if (string.IsNullOrEmpty(text)) return;

            // 设置文字格式
            StringFormat format = new StringFormat
            {
                Alignment = StringAlignment.Near,
                LineAlignment = StringAlignment.Near,
                Trimming = StringTrimming.Word,
                FormatFlags = StringFormatFlags.LineLimit
            };

            // 在单元格内绘制文字，自动换行
            Rectangle textRect = new Rectangle(
                cellRect.X + 2,
                cellRect.Y + 2,
                cellRect.Width - 4,
                cellRect.Height - 4
            );

            g.DrawString(text, font, brush, textRect, format);

            format.Dispose();
        }

        /// <summary>
        /// 在表头单元格中绘制文字（居中对齐，支持自动换行）
        /// </summary>
        private void DrawTextInHeaderCell(Graphics g, string text, Rectangle cellRect, Font font, Brush brush)
        {
            if (string.IsNullOrEmpty(text)) return;

            // 设置文字格式 - 表头居中对齐
            StringFormat format = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center,
                Trimming = StringTrimming.Word,
                FormatFlags = StringFormatFlags.LineLimit
            };

            // 在单元格内绘制文字，自动换行和居中
            Rectangle textRect = new Rectangle(
                cellRect.X + 1,
                cellRect.Y + 1,
                cellRect.Width - 2,
                cellRect.Height - 2
            );

            g.DrawString(text, font, brush, textRect, format);

            format.Dispose();
        }

        /// <summary>
        /// 绘制二维码定位标记
        /// </summary>
        private void DrawQRPositionMarker(Graphics g, float x, float y, float size)
        {
            // 外框
            g.FillRectangle(Brushes.Black, x, y, size, size);
            // 内部白色
            g.FillRectangle(Brushes.White, x + size/4, y + size/4, size/2, size/2);
            // 中心黑点
            g.FillRectangle(Brushes.Black, x + size*3/8, y + size*3/8, size/4, size/4);
        }

        /// <summary>
        /// 绘制标题
        /// </summary>
        private void DrawTitle(Graphics g, float x, float y, float width)
        {
            string title = "发货通知单";
            SizeF titleSize = g.MeasureString(title, _headerFont);
            float titleX = x + (width - titleSize.Width) / 2;
            g.DrawString(title, _headerFont, Brushes.Black, titleX, y);

            // 右上角信息已经在DrawBarcode方法中绘制，这里不再重复
        }

        /// <summary>
        /// 绘制基本信息
        /// </summary>
        private float DrawBasicInfo(Graphics g, float x, float y, float width)
        {
            float currentY = y;
            float lineHeight = 22; // 增加行高以适应更大的字体

            // 使用百分比布局，更好地适应不同页面宽度
            float col1X = x;
            float col2X = x + width * 0.35f;  // 35% 位置
            float col3X = x + width * 0.7f;   // 70% 位置

            // 第一行 - 使用百分比布局避免重叠
            g.DrawString($"客户单位: {_currentShippingNote.CustomerUnit}", _contentFont, Brushes.Black, col1X, currentY);
            g.DrawString($"发货仓库: {_currentShippingNote.ShippingWarehouse}", _contentFont, Brushes.Black, col2X, currentY);
            g.DrawString($"联系人: {_currentShippingNote.ContactPerson}", _contentFont, Brushes.Black, col3X, currentY);
            currentY += lineHeight;

            g.DrawString($"日期: {_currentShippingNote.ShippingDate:yyyy-MM-dd}", _contentFont, Brushes.Black, col2X, currentY);
            g.DrawString($"电话: {_currentShippingNote.Phone}", _contentFont, Brushes.Black, col3X, currentY);
            currentY += lineHeight;

            // 第二行 - 使用百分比布局避免重叠
            g.DrawString($"市场区域: {_currentShippingNote.MarketArea}", _contentFont, Brushes.Black, col1X, currentY);
            g.DrawString($"交货方式: {_currentShippingNote.DeliveryMethod}", _contentFont, Brushes.Black, col2X, currentY);
            currentY += lineHeight;

            // 第三行
            g.DrawString($"收货地址: {_currentShippingNote.DeliveryAddress}", _contentFont, Brushes.Black, col1X, currentY);
            currentY += lineHeight;

            // 备注说明
            if (!string.IsNullOrEmpty(_currentShippingNote.Remarks))
            {
                g.DrawString($"备注说明: {_currentShippingNote.Remarks}", _contentFont, Brushes.Black, col1X, currentY);
                currentY += lineHeight;
            }

            // 特殊说明
            if (!string.IsNullOrEmpty(_currentShippingNote.SpecialInstructions))
            {
                g.DrawString($"特殊说明: {_currentShippingNote.SpecialInstructions}", _contentFont, Brushes.Black, col1X, currentY);
                currentY += lineHeight;
            }

            return currentY;
        }

        /// <summary>
        /// 绘制明细表格
        /// </summary>
        private float DrawItemsTable(Graphics g, float x, float y, float width)
        {
            float currentY = y;
            float rowHeight = 28; // 进一步增加行高以适应更大的字体
            float headerHeight = 45; // 进一步增加表头高度以适应更大的字体
            
            // 表格列宽定义 - 重新优化：数量、数、总体积列更宽，避免换行
            float[] columnWidths = { 35, 270, 35, 70, 70, 65, 75 };
            float[] columnX = new float[columnWidths.Length];
            columnX[0] = x;
            for (int i = 1; i < columnWidths.Length; i++)
            {
                columnX[i] = columnX[i - 1] + columnWidths[i - 1];
            }

            // 绘制表头 - 新的列结构
            string[] headers = { "序号", "品名规格 (mm)", "单位", "数量", "标长/容量", "数", "总体积" };
            
            // 表头背景 - 取消背景色，只绘制边框
            Rectangle headerRect = new Rectangle((int)x, (int)currentY, (int)width, (int)headerHeight);
            g.DrawRectangle(Pens.Black, headerRect);

            for (int i = 0; i < headers.Length && i < columnX.Length; i++)
            {
                float columnWidth = (i < columnWidths.Length) ? columnWidths[i] : 60;
                Rectangle headerCellRect = new Rectangle((int)columnX[i], (int)currentY, (int)columnWidth, (int)headerHeight);

                // 使用DrawTextInHeaderCell方法绘制表头文字，支持自动换行和居中对齐
                DrawTextInHeaderCell(g, headers[i], headerCellRect, _boldFont, Brushes.Black);

                if (i < columnX.Length - 1)
                {
                    g.DrawLine(Pens.Black, columnX[i + 1], currentY, columnX[i + 1], currentY + headerHeight);
                }
            }
            currentY += headerHeight;

            // 绘制数据行 - 支持分组显示
            if (_currentShippingNote.Items != null)
            {
                int itemSequence = 1; // 为实际物料明细行生成连续序号
                foreach (var item in _currentShippingNote.Items)
                {
                    if (item.IsGroupHeader)
                    {
                        // 绘制分组标题行 - 显示汇总数据，分组行不显示序号
                        string[] groupValues = {
                            "", // 分组行不显示序号
                            item.ProductSpecification, // MATERIALSHORTNAME
                            item.Unit,
                            item.Quantity.ToString("F0"),
                            "", // 标长/容量在分组行为空
                            item.Count.ToString("F0"),
                            item.TotalVolume.ToString("F4")
                        };

                        float groupRowHeight = 33; // 进一步增加分组行高度以适应更大的字体
                        Rectangle groupRowRect = new Rectangle((int)x, (int)currentY, (int)width, (int)groupRowHeight);
                        g.DrawRectangle(Pens.Black, groupRowRect); // 取消背景色，只绘制边框

                        // 绘制分组行的各列数据 - 整行使用加粗字体
                        for (int i = 0; i < groupValues.Length && i < columnX.Length; i++)
                        {
                            float columnWidth = (i < columnWidths.Length) ? columnWidths[i] : 60;
                            Rectangle cellRect = new Rectangle((int)columnX[i], (int)currentY, (int)columnWidth, (int)groupRowHeight);

                            // 分组行整行使用加粗字体，并且居中显示
                            StringFormat centerFormat = new StringFormat
                            {
                                Alignment = StringAlignment.Center,
                                LineAlignment = StringAlignment.Center
                            };
                            g.DrawString(groupValues[i], _boldFont, Brushes.Black, cellRect, centerFormat);

                            // 绘制列分隔线
                            if (i < columnX.Length - 1)
                            {
                                g.DrawLine(Pens.Black, columnX[i + 1], currentY, columnX[i + 1], currentY + groupRowHeight);
                            }
                        }
                        currentY += groupRowHeight;
                    }
                    else
                    {
                        // 绘制普通物料行 - 使用连续序号
                        string[] values = {
                            itemSequence.ToString(), // 使用连续序号
                            item.ProductSpecification,
                            item.Unit,
                            item.Quantity.ToString("F0"),
                            item.StandardLength, // 标长/容量 (字符串)
                            item.Count.ToString("F0"),
                            item.TotalVolume.ToString("F4")
                        };

                        // 计算这一行需要的实际高度（考虑换行）
                        float actualRowHeight = CalculateRowHeight(g, values, columnWidths, columnX, _contentFont);

                        // 绘制行边框
                        Rectangle rowRect = new Rectangle((int)x, (int)currentY, (int)width, (int)actualRowHeight);
                        g.DrawRectangle(Pens.Black, rowRect);

                        // 绘制每列的内容
                        for (int i = 0; i < values.Length && i < columnX.Length; i++)
                        {
                            float columnWidth = (i < columnWidths.Length) ? columnWidths[i] : 60;
                            Rectangle cellRect = new Rectangle((int)columnX[i], (int)currentY, (int)columnWidth, (int)actualRowHeight);

                            // 绘制文字（支持换行）
                            DrawTextInCell(g, values[i], cellRect, _contentFont, Brushes.Black);

                            // 绘制列分隔线
                            if (i < columnX.Length - 1)
                            {
                                g.DrawLine(Pens.Black, columnX[i + 1], currentY, columnX[i + 1], currentY + actualRowHeight);
                            }
                        }
                        currentY += actualRowHeight;
                        itemSequence++; // 递增序号，为下一个物料明细行准备
                    }
                }
            }

            return currentY;
        }

        /// <summary>
        /// 绘制底部信息
        /// </summary>
        private void DrawFooterInfo(Graphics g, float x, float y, float width)
        {
            float lineHeight = 22; // 增加行高以适应更大的字体
            float currentY = y;

            // 统计信息 - 计算当前页和总计
            var currentPageItems = _currentShippingNote.Items?.Where(i => !i.IsGroupHeader) ?? new List<ShippingNoteItem>();

            // 本页统计（只计算非分组标题行）
            decimal currentPageCount = currentPageItems.Sum(i => i.Count); // 本页数量小计：数(ENTRIESQTY)的和
            decimal currentPageWeight = currentPageItems.Sum(i => i.Weight123); // 本页重量小计：MATERIALGROSSWEIGHT123的和
            decimal currentPageVolume = currentPageItems.Sum(i => i.TotalVolume); // 本页体积小计：MATERIALVOLUME * ENTRIESQTY的和

            // 总量总计：使用预先计算的所有页面总重量
            decimal totalWeight = _currentShippingNote.TotalWeight;

            // 计算四等分位置
            float col1X = x;
            float col2X = x + width * 0.25f;
            float col3X = x + width * 0.5f;
            float col4X = x + width * 0.75f;

            g.DrawString($"重量总计: {totalWeight:F4}", _contentFont, Brushes.Black, col1X, currentY);
            g.DrawString($"本页重量小计: {currentPageWeight:F3}", _contentFont, Brushes.Black, col2X, currentY);
            g.DrawString($"本页数量小计: {currentPageCount}", _contentFont, Brushes.Black, col3X, currentY);
            g.DrawString($"本页体积小计: {currentPageVolume:F6}", _contentFont, Brushes.Black, col4X, currentY);
            currentY += lineHeight * 2.5f; // 增加统计信息和签名区域之间的间距

            // 签名区域 - 重新分布为四等分，更均匀美观
            float col1XSig = x;
            float col2XSig = x + width * 0.25f;
            float col3XSig = x + width * 0.5f;
            float col4XSig = x + width * 0.75f;

            // 第一行：装货单号、车长、手工米数、货物重量
            g.DrawString($"装货单号: {_currentShippingNote.VehicleNumber}", _contentFont, Brushes.Black, col1XSig, currentY);
            g.DrawString($"车长: {_currentShippingNote.VehicleLength}", _contentFont, Brushes.Black, col2XSig, currentY);
            g.DrawString($"手工米数: {_currentShippingNote.ManualMeters:F2}", _contentFont, Brushes.Black, col3XSig, currentY);
            g.DrawString($"货物重量: {_currentShippingNote.CargoWeight:F2}", _contentFont, Brushes.Black, col4XSig, currentY);
            currentY += lineHeight;

            // 第二行：业务员、主管、制单、审核
            g.DrawString($"业务员: {_currentShippingNote.Salesperson}", _contentFont, Brushes.Black, col1XSig, currentY);
            g.DrawString($"主管: {_currentShippingNote.Supervisor}", _contentFont, Brushes.Black, col2XSig, currentY);
            g.DrawString($"制单: {_currentShippingNote.Creator}", _contentFont, Brushes.Black, col3XSig, currentY);
            g.DrawString($"审核: {_currentShippingNote.Reviewer}", _contentFont, Brushes.Black, col4XSig, currentY);
            currentY += lineHeight;

            // 最后一行：打印日期、是否加货（取消Q300009）
            g.DrawString($"最后打印日期: {DateTime.Now:MM月dd日HH:mm}", _contentFont, Brushes.Black, col1XSig, currentY);
            g.DrawString($"是否加货: {(_currentShippingNote.IsUrgent ? "是" : "否")}", _contentFont, Brushes.Black, col2XSig, currentY);
            // 取消 Q300009 显示
        }

        /// <summary>
        /// 应用纸张大小设置到打印文档
        /// </summary>
        /// <param name="printDoc">打印文档</param>
        private void ApplyPaperSizeSettings(PrintDocument printDoc)
        {
            // 纸张大小配置：根据配置决定是否自动适配
            bool autoAdaptPaperSize = bool.Parse(ConfigurationManager.AppSettings["AutoAdaptPaperSize"] ?? "true");

            if (!autoAdaptPaperSize)
            {
                // 强制设置纸张大小
                int paperWidth = int.Parse(ConfigurationManager.AppSettings["ForcePaperWidth"] ?? "827");
                int paperHeight = int.Parse(ConfigurationManager.AppSettings["ForcePaperHeight"] ?? "1169");
                printDoc.DefaultPageSettings.PaperSize = new PaperSize("Custom", paperWidth, paperHeight);
            }
            // 如果autoAdaptPaperSize=true，则使用打印机默认纸张大小，适合针式打印机

            // 设置边距
            printDoc.DefaultPageSettings.Margins = new Margins(15, 15, 30, 30);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _printDocument?.Dispose();
            _headerFont?.Dispose();
            _contentFont?.Dispose();
            _smallFont?.Dispose();
            _boldFont?.Dispose();
        }

        /// <summary>
        /// 根据MATERIALNAME1创建分页的发运单
        /// </summary>
        /// <param name="shippingNotes">原始发运单列表</param>
        /// <returns>分页后的发运单列表</returns>
        private List<ShippingNote> CreatePagedShippingNotes(List<ShippingNote> shippingNotes)
        {
            var pagedNotes = new List<ShippingNote>();

            foreach (var originalNote in shippingNotes)
            {
                if (originalNote.Items == null || !originalNote.Items.Any())
                {
                    pagedNotes.Add(originalNote);
                    continue;
                }

                // 计算所有物料的总重量 (MATERIALGROSSWEIGHT123)
                decimal totalWeight = originalNote.Items
                    .Where(item => !item.IsGroupHeader)
                    .Sum(item => item.Weight123);

                // 按MATERIALNAME1分组进行分页
                var materialName1Groups = originalNote.Items
                    .Where(item => !item.IsGroupHeader) // 排除分组标题行
                    .GroupBy(item => item.MaterialName1 ?? "")
                    .ToList();

                if (materialName1Groups.Count <= 1)
                {
                    // 只有一个MATERIALNAME1分组，不需要分页
                    originalNote.TotalPages = 1;
                    originalNote.CurrentPage = 1;
                    originalNote.TotalWeight = totalWeight;
                    pagedNotes.Add(originalNote);
                }
                else
                {
                    // 需要分页，每个MATERIALNAME1一页
                    int pageNumber = 1;
                    int totalPages = materialName1Groups.Count;

                    foreach (var materialGroup in materialName1Groups)
                    {
                        var pagedNote = CloneShippingNoteWithoutItems(originalNote);
                        pagedNote.CurrentPage = pageNumber;
                        pagedNote.TotalPages = totalPages;
                        pagedNote.TotalWeight = totalWeight; // 所有页面共享相同的总重量

                        // 重新构建该页的物料项目（包含分组标题）
                        RebuildItemsForPage(pagedNote, materialGroup.ToList());

                        pagedNotes.Add(pagedNote);
                        pageNumber++;
                    }
                }
            }

            return pagedNotes;
        }

        /// <summary>
        /// 克隆发运单但不包含明细项目
        /// </summary>
        private ShippingNote CloneShippingNoteWithoutItems(ShippingNote original)
        {
            return new ShippingNote
            {
                CustomerUnit = original.CustomerUnit,
                ShippingWarehouse = original.ShippingWarehouse,
                ContactPerson = original.ContactPerson,
                Phone = original.Phone,
                ShippingDate = original.ShippingDate,
                MarketArea = original.MarketArea,
                DeliveryMethod = original.DeliveryMethod,
                DeliveryAddress = original.DeliveryAddress,
                Remarks = original.Remarks,
                SpecialInstructions = original.SpecialInstructions,
                LoadingNumber = original.LoadingNumber,
                VehicleNumber = original.VehicleNumber,
                VehicleLength = original.VehicleLength,
                ManualMeters = original.ManualMeters,
                CargoWeight = original.CargoWeight,
                Salesperson = original.Salesperson,
                Supervisor = original.Supervisor,
                Creator = original.Creator,
                Reviewer = original.Reviewer,
                IsUrgent = original.IsUrgent,
                OrderNumber = original.OrderNumber,
                PrintNumber = original.PrintNumber,
                BarcodeContent = original.BarcodeContent,
                QRCodeContent = original.QRCodeContent,
                PostRequisitionNumber = original.PostRequisitionNumber,
                CreateDate = original.CreateDate,
                TotalWeight = original.TotalWeight
            };
        }

        /// <summary>
        /// 为指定页面重新构建物料项目
        /// </summary>
        private void RebuildItemsForPage(ShippingNote pagedNote, List<ShippingNoteItem> materialItems)
        {
            pagedNote.Items = new List<ShippingNoteItem>();

            // 按MATERIALSHORTNAME分组
            var groupedItems = materialItems
                .GroupBy(item => item.MaterialShortName ?? "")
                .OrderBy(g => g.Key)
                .ToList();

            int sequenceNumber = 1;

            foreach (var group in groupedItems)
            {
                // 计算分组汇总数据
                decimal groupQuantity = group.Sum(item => item.Quantity);
                decimal groupCount = group.Sum(item => item.Count);
                decimal groupTotalVolume = group.Sum(item => item.TotalVolume);

                // 添加分组标题行（包含汇总数据）
                var groupHeader = new ShippingNoteItem
                {
                    SequenceNumber = sequenceNumber++,
                    ProductSpecification = group.Key, // MATERIALSHORTNAME
                    IsGroupHeader = true,
                    MaterialShortName = group.Key,
                    Unit = "箱", // 分组行单位
                    Quantity = groupQuantity,
                    Count = groupCount,
                    TotalVolume = groupTotalVolume
                };
                pagedNote.Items.Add(groupHeader);

                // 添加该分组下的物料明细
                foreach (var item in group.OrderBy(i => i.ProductSpecification))
                {
                    item.SequenceNumber = sequenceNumber++;
                    pagedNote.Items.Add(item);
                }
            }
        }
    }
}
