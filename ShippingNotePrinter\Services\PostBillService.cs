using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ShippingNotePrinter.Models;

namespace ShippingNotePrinter.Services
{
    /// <summary>
    /// PostBill服务，用于调用getPostBill和addkaptcha接口
    /// </summary>
    public class PostBillService
    {
        private readonly string _serviceUrl;
        private readonly int _timeout;
        private string _lastUuid; // 保存上次获取验证码时的UUID

        public PostBillService()
        {
            _serviceUrl = ConfigurationManager.AppSettings["PostBillServiceUrl"] ?? 
                "http://************:8980/axis2/services/PrintPostrequestBill.PrintPostrequestBillHttpSoap12Endpoint/";
            _timeout = 30000; // 30秒超时
        }

        /// <summary>
        /// 调用getPostBill接口检查手机号是否有发运数据
        /// </summary>
        /// <param name="phoneNumber">手机号</param>
        /// <returns>是否存在发运数据</returns>
        public bool CheckPostBillExists(string phoneNumber)
        {
            try
            {
                string result = CallAxis2Service("getPostBill", phoneNumber, "mark");

                // 打印返回结果用于调试
                Console.WriteLine($"getPostBill返回结果: {result}");

                // 如果返回不是 {"postrequestBill":[]}，说明手机号存在发运数据

                if (!string.IsNullOrEmpty(result) && result.Trim() != "{\"postrequestBill\":[]}")
                {
                    Console.WriteLine("检测到发运数据存在");
                    return true;
                }

                Console.WriteLine("未检测到发运数据");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"调用getPostBill接口异常: {ex.Message}");
                throw new Exception($"检查发运数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用addkaptcha接口发送验证码
        /// </summary>
        /// <param name="phoneNumber">手机号</param>
        /// <returns>是否发送成功</returns>
        public bool SendVerificationCode(string phoneNumber)
        {
            try
            {
                // 构造JSON参数
                var kaptchaData = new
                {
                    source = "eas-printpostrequest",
                    user = phoneNumber,
                    mark = "",
                    sign = "",
                    phone = phoneNumber
                };

                string jsonParam = JsonConvert.SerializeObject(kaptchaData);
                Console.WriteLine($"addkaptcha请求参数: {jsonParam}");

                string result = CallAxis2Service("addkaptcha", jsonParam);

                // 打印返回结果用于调试
                Console.WriteLine($"addkaptcha返回结果: {result}");

                // 解析JSON响应
                if (!string.IsNullOrEmpty(result))
                {
                    try
                    {
                        var jsonResponse = JObject.Parse(result);
                        var code = jsonResponse["code"]?.Value<int>();

                        if (code == 20000)
                        {
                            // 获取UUID用于后续验证
                            _lastUuid = jsonResponse["data"]?["uuid"]?.Value<string>();
                            Console.WriteLine($"验证码发送成功，UUID: {_lastUuid}");
                            return true;
                        }
                        else
                        {
                            var message = jsonResponse["message"]?.Value<string>() ?? "未知错误";
                            Console.WriteLine($"验证码发送失败，错误码: {code}，消息: {message}");
                            return false;
                        }
                    }
                    catch (JsonException ex)
                    {
                        Console.WriteLine($"解析JSON响应失败: {ex.Message}");
                        return false;
                    }
                }

                Console.WriteLine("验证码发送失败：返回结果为空");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"调用addkaptcha接口异常: {ex.Message}");
                throw new Exception($"发送验证码失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用checkkaptcha接口验证验证码
        /// </summary>
        /// <param name="verificationCode">用户输入的验证码</param>
        /// <returns>验证是否成功</returns>
        public bool VerifyCode(string verificationCode)
        {
            try
            {
                if (string.IsNullOrEmpty(_lastUuid))
                {
                    Console.WriteLine("验证失败：没有有效的UUID，请先发送验证码");
                    return false;
                }

                // 构造JSON参数
                var checkData = new
                {
                    fid = _lastUuid,
                    kaptcha = verificationCode
                };

                string jsonParam = JsonConvert.SerializeObject(checkData);
                Console.WriteLine($"checkkaptcha请求参数: {jsonParam}");

                string result = CallAxis2Service("checkkaptcha", jsonParam);

                // 打印返回结果用于调试
                Console.WriteLine($"checkkaptcha返回结果: {result}");

                // 解析JSON响应
                if (!string.IsNullOrEmpty(result))
                {
                    try
                    {
                        var jsonResponse = JObject.Parse(result);
                        var code = jsonResponse["code"]?.Value<int>();

                        if (code == 20000)
                        {
                            Console.WriteLine("验证码校验成功");
                            return true;
                        }
                        else
                        {
                            var message = jsonResponse["message"]?.Value<string>() ?? "未知错误";
                            Console.WriteLine($"验证码校验失败，错误码: {code}，消息: {message}");
                            return false;
                        }
                    }
                    catch (JsonException ex)
                    {
                        Console.WriteLine($"解析JSON响应失败: {ex.Message}");
                        return false;
                    }
                }

                Console.WriteLine("验证码校验失败：返回结果为空");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"调用checkkaptcha接口异常: {ex.Message}");
                throw new Exception($"验证验证码失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取上次发送验证码时的UUID
        /// </summary>
        /// <returns>UUID字符串</returns>
        public string GetLastUuid()
        {
            return _lastUuid;
        }

        /// <summary>
        /// 获取发运单明细列表
        /// </summary>
        /// <param name="phoneNumber">手机号</param>
        /// <returns>发运单列表</returns>
        public List<ShippingNote> GetShippingNotesByPhone(string phoneNumber)
        {
            try
            {
                string result = CallAxis2Service("getPostBill", phoneNumber, "mark");

                // 打印返回结果用于调试
                Console.WriteLine($"getPostBill返回结果: {result}");

                if (string.IsNullOrEmpty(result) || result.Trim() == "{\"postrequestBill\":[]}")
                {
                    return new List<ShippingNote>();
                }

                // 解析JSON数据
                var jsonResponse = JObject.Parse(result);
                var postBillArray = jsonResponse["postrequestBill"] as JArray;

                if (postBillArray == null || postBillArray.Count == 0)
                {
                    return new List<ShippingNote>();
                }

                // 按装货单号分组（CARRYBILLNUMBER相同的是同一个发运单）
                var groupedData = new Dictionary<string, List<JObject>>();
                foreach (JObject item in postBillArray)
                {
                    string carryBillNumber = item["CARRYBILLNUMBER"]?.Value<string>() ?? "";
                    if (!groupedData.ContainsKey(carryBillNumber))
                    {
                        groupedData[carryBillNumber] = new List<JObject>();
                    }
                    groupedData[carryBillNumber].Add(item);
                }

                // 转换为发运单列表
                var shippingNotes = new List<ShippingNote>();
                foreach (var group in groupedData)
                {
                    var firstItem = group.Value[0];
                    var shippingNote = CreateShippingNoteFromJson(firstItem, group.Value);
                    shippingNotes.Add(shippingNote);
                }

                return shippingNotes;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取发运单明细异常: {ex.Message}");
                throw new Exception($"获取发运单明细失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从JSON数据创建发运单对象
        /// </summary>
        /// <param name="firstItem">第一个明细项（用于获取表头信息）</param>
        /// <param name="allItems">所有明细项</param>
        /// <returns>发运单对象</returns>
        private ShippingNote CreateShippingNoteFromJson(JObject firstItem, List<JObject> allItems)
        {
            var shippingNote = new ShippingNote
            {
                // 使用装货单号作为发运单号（因为按CARRYBILLNUMBER分组）
                ShippingNoteNumber = firstItem["CARRYBILLNUMBER"]?.Value<string>() ?? "",
                CustomerUnit = firstItem["ORDERCUSTOMERNAME"]?.Value<string>() ?? "",
                ShippingWarehouse = firstItem["WAREHOUSENAME"]?.Value<string>() ?? "",
                DeliveryMethod = firstItem["DELIVERYTYPENAME"]?.Value<string>() ?? "",
                OrderNumber = firstItem["ENTRIESSALEORDERNUMBER"]?.Value<string>() ?? "",
                PrintNumber = firstItem["ENTRIESSALEORDERNUMBER"]?.Value<string>() ?? "", // 打单号改为订单号
                ContactPerson = firstItem["SALEORDERCONTRACTNAME"]?.Value<string>() ?? "",
                Phone = firstItem["SALEORDERCONTRACTPHONE"]?.Value<string>() ?? "",
                MarketArea = firstItem["SALEGROUPNAME"]?.Value<string>() ?? "",
                DeliveryAddress = firstItem["ENTRIESDELIVERYADDRESS"]?.Value<string>() ?? "",
                Remarks = firstItem["DESCRIPTION"]?.Value<string>() ?? "",
                SpecialInstructions = firstItem["KDTEXTAREA"]?.Value<string>() ?? "",
                LoadingNumber = firstItem["CARRYBILLNUMBER"]?.Value<string>() ?? "", // 装货单号(用于分组)
                VehicleNumber = firstItem["VEHICLENUMBER"]?.Value<string>() ?? "", // 装载单号(车牌号，用于打印显示)
                VehicleLength = firstItem["VEHICLELENGTH"]?.Value<string>() ?? "",
                Salesperson = firstItem["SALEPERSONNAME"]?.Value<string>() ?? "",
                Creator = firstItem["CREATORNAME"]?.Value<string>() ?? "",
                Reviewer = firstItem["AUDITORNAME"]?.Value<string>() ?? "",
                // 添加条形码和二维码内容 - 使用SON开头的订单号
                BarcodeContent = firstItem["ENTRIESSALEORDERNUMBER"]?.Value<string>() ?? "",
                QRCodeContent = firstItem["ENTRIESSALEORDERNUMBER"]?.Value<string>() ?? "",
                // 添加NO字段内容 - 使用POSTREQUISITIONNUMBER
                PostRequisitionNumber = firstItem["POSTREQUISITIONNUMBER"]?.Value<string>() ?? ""
            };

            // 解析日期
            string shippingDateStr = firstItem["BIZDATE"]?.Value<string>();
            if (DateTime.TryParse(shippingDateStr, out DateTime shippingDate))
                shippingNote.ShippingDate = shippingDate;

            string auditTimeStr = firstItem["AUDITTIME"]?.Value<string>();
            if (DateTime.TryParse(auditTimeStr, out DateTime createDate))
                shippingNote.CreateDate = createDate;

            // 解析数值字段
            string manualMetersStr = firstItem["VEHICLEMANUAL"]?.Value<string>();
            if (decimal.TryParse(manualMetersStr, out decimal manualMeters))
                shippingNote.ManualMeters = manualMeters;

            string cargoWeightStr = firstItem["VEHICLEWEIGHT"]?.Value<string>();
            if (decimal.TryParse(cargoWeightStr, out decimal cargoWeight))
                shippingNote.CargoWeight = cargoWeight;

            // 解析是否加货字段 (ISNOTADDGOODS: 1=是, 2=否)
            string isNotAddGoodsStr = firstItem["ISNOTADDGOODS"]?.Value<string>();
            if (int.TryParse(isNotAddGoodsStr, out int isNotAddGoods))
                shippingNote.IsUrgent = (isNotAddGoods == 1); // 1表示加货，2表示不加货

            // 创建明细项目 - 按MATERIALSHORTNAME分组
            CreateGroupedShippingNoteItems(shippingNote, allItems);

            return shippingNote;
        }

        /// <summary>
        /// 创建分组的发运单明细项目
        /// </summary>
        /// <param name="shippingNote">发运单</param>
        /// <param name="allItems">所有明细项</param>
        private void CreateGroupedShippingNoteItems(ShippingNote shippingNote, List<JObject> allItems)
        {
            // 按MATERIALSHORTNAME分组
            var groupedItems = allItems
                .GroupBy(item => item["MATERIALSHORTNAME"]?.Value<string>() ?? "")
                .OrderBy(g => g.Key)
                .ToList();

            int sequenceNumber = 1;

            foreach (var group in groupedItems)
            {
                // 计算分组汇总数据
                var groupItems = group.Select(item => CreateShippingNoteItemFromJson(item, 0)).ToList();
                decimal totalQuantity = groupItems.Sum(item => item.Quantity);
                decimal totalCount = groupItems.Sum(item => item.Count);
                decimal totalVolume = groupItems.Sum(item => item.TotalVolume);

                // 添加分组标题行（包含汇总数据）
                var groupHeader = new ShippingNoteItem
                {
                    SequenceNumber = sequenceNumber++,
                    ProductSpecification = group.Key, // MATERIALSHORTNAME
                    IsGroupHeader = true,
                    MaterialShortName = group.Key,
                    Quantity = totalQuantity,
                    Count = totalCount,
                    TotalVolume = totalVolume,
                    Unit = "箱" // 分组行单位
                };
                shippingNote.Items.Add(groupHeader);

                // 添加该分组下的物料明细
                foreach (var item in group.OrderBy(i => i["MATERIALNAME"]?.Value<string>()))
                {
                    var shippingNoteItem = CreateShippingNoteItemFromJson(item, sequenceNumber++);
                    shippingNote.Items.Add(shippingNoteItem);
                }
            }
        }

        /// <summary>
        /// 从JSON数据创建发运单明细项
        /// </summary>
        /// <param name="item">JSON明细项</param>
        /// <param name="sequenceNumber">序号</param>
        /// <returns>发运单明细项</returns>
        private ShippingNoteItem CreateShippingNoteItemFromJson(JObject item, int sequenceNumber)
        {
            var shippingNoteItem = new ShippingNoteItem
            {
                SequenceNumber = sequenceNumber,
                ProductSpecification = item["MATERIALNAME"]?.Value<string>() ?? "",
                Unit = item["UNITNAME"]?.Value<string>() ?? "",
                Remarks = item["FZZD1"]?.Value<string>() ?? "",
                MaterialShortName = item["MATERIALSHORTNAME"]?.Value<string>() ?? "",
                MaterialName1 = item["MATERIALNAME1"]?.Value<string>() ?? "",
                IsGroupHeader = false
            };

            // 解析数值字段
            string quantityStr = item["ENTRIESASSISTQTY"]?.Value<string>();
            if (decimal.TryParse(quantityStr, out decimal quantity))
                shippingNoteItem.Quantity = quantity;

            string countStr = item["ENTRIESQTY"]?.Value<string>();
            if (decimal.TryParse(countStr, out decimal count))
                shippingNoteItem.Count = count;

            string unitWeightStr = item["MATERIALGROSSWEIGHT"]?.Value<string>();
            if (decimal.TryParse(unitWeightStr, out decimal unitWeight))
                shippingNoteItem.UnitWeight = unitWeight;

            string weight123Str = item["MATERIALGROSSWEIGHT123"]?.Value<string>();
            if (decimal.TryParse(weight123Str, out decimal weight123))
                shippingNoteItem.Weight123 = weight123;

            string unitVolumeStr = item["MATERIALVOLUME"]?.Value<string>();
            if (decimal.TryParse(unitVolumeStr, out decimal unitVolume))
                shippingNoteItem.UnitVolume = unitVolume;

            // 设置标长/容量字段
            shippingNoteItem.StandardLength = item["MATERIALHELPCODE"]?.Value<string>() ?? "";

            // 计算总体积
            shippingNoteItem.CalculateTotalVolume();

            return shippingNoteItem;
        }

        /// <summary>
        /// 调用Axis2服务的通用方法
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="param1">参数1</param>
        /// <param name="param2">参数2（可选）</param>
        /// <returns>服务返回结果</returns>
        private string CallAxis2Service(string operationName, string param1, string param2 = null)
        {
            try
            {
                // 创建SOAP请求
                string soapEnvelope;
                
                if (param2 != null)
                {
                    // 两个参数的情况（如getPostBill）
                    soapEnvelope = CreateSoapEnvelope(operationName, param1, param2);
                }
                else
                {
                    // 一个参数的情况（如addkaptcha）
                    soapEnvelope = CreateSoapEnvelope(operationName, param1);
                }

                return SendSoapRequest(soapEnvelope, operationName);
            }
            catch (Exception ex)
            {
                throw new Exception($"调用Axis2服务失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建SOAP信封（两个参数）
        /// </summary>
        private string CreateSoapEnvelope(string operationName, string param1, string param2)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
<soap:Envelope xmlns:soap=""http://schemas.xmlsoap.org/soap/envelope/""
               xmlns:axis2=""http://ws.apache.org/axis2"">
    <soap:Header/>
    <soap:Body>
        <axis2:{operationName}>
            <args0>{param1}</args0>
            <args1>{param2}</args1>
        </axis2:{operationName}>
    </soap:Body>
</soap:Envelope>";
        }

        /// <summary>
        /// 创建SOAP信封（一个参数）
        /// </summary>
        private string CreateSoapEnvelope(string operationName, string param1)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
<soap:Envelope xmlns:soap=""http://schemas.xmlsoap.org/soap/envelope/""
               xmlns:axis2=""http://ws.apache.org/axis2"">
    <soap:Header/>
    <soap:Body>
        <axis2:{operationName}>
            <args0>{param1}</args0>
        </axis2:{operationName}>
    </soap:Body>
</soap:Envelope>";
        }

        /// <summary>
        /// 发送SOAP请求
        /// </summary>
        private string SendSoapRequest(string soapEnvelope, string soapAction)
        {
            Console.WriteLine($"发送SOAP请求到: {_serviceUrl}");
            Console.WriteLine($"SOAP Action: {soapAction}");
            Console.WriteLine($"SOAP请求内容:\n{soapEnvelope}");

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(_serviceUrl);
            request.Method = "POST";
            request.ContentType = "text/xml; charset=utf-8";
            request.Headers.Add("SOAPAction", $"\"{soapAction}\"");
            request.Timeout = _timeout;

            // 写入请求数据
            byte[] data = Encoding.UTF8.GetBytes(soapEnvelope);
            request.ContentLength = data.Length;

            using (var requestStream = request.GetRequestStream())
            {
                requestStream.Write(data, 0, data.Length);
            }

            // 获取响应
            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            using (var reader = new System.IO.StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                string responseXml = reader.ReadToEnd();
                return ExtractResultFromSoapResponse(responseXml);
            }
        }

        /// <summary>
        /// 从SOAP响应中提取结果
        /// </summary>
        private string ExtractResultFromSoapResponse(string soapResponse)
        {
            try
            {
                // 简单的XML解析，提取返回值
                // 根据实际的SOAP响应格式调整
                var doc = new System.Xml.XmlDocument();
                doc.LoadXml(soapResponse);

                var nsManager = new System.Xml.XmlNamespaceManager(doc.NameTable);
                nsManager.AddNamespace("soap", "http://schemas.xmlsoap.org/soap/envelope/");
                nsManager.AddNamespace("axis2", "http://ws.apache.org/axis2");

                // 查找返回值节点
                var returnNode = doc.SelectSingleNode("//soap:Body//*[local-name()='return']", nsManager);
                if (returnNode != null)
                {
                    return returnNode.InnerText;
                }

                // 如果没找到return节点，返回整个Body内容
                var bodyNode = doc.SelectSingleNode("//soap:Body", nsManager);
                return bodyNode?.InnerXml ?? soapResponse;
            }
            catch (Exception)
            {
                // 如果XML解析失败，返回原始响应
                return soapResponse;
            }
        }
    }
}
