<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    
    <appSettings>
        <!-- Tomcat SOAP服务配置 -->
        <add key="SoapServiceUrl" value="http://localhost:8080/axis2/services/ShippingNoteService" />
        <add key="SmsServiceUrl" value="http://localhost:8080/axis2/services/SmsService" />

        <!-- 新的Axis2服务配置 -->
        <add key="PostBillServiceUrl" value="http://***********:8980/axis2/services/PrintPostrequestBill.PrintPostrequestBillHttpSoap12Endpoint/" />

        <!-- 打印机配置 -->
        <add key="DefaultPrinterName" value="" />
        <!-- 纸张适配配置：true=自动适配纸张大小(推荐针式打印机), false=强制A4纸张 -->
        <add key="AutoAdaptPaperSize" value="true" />
        <!-- 强制纸张大小配置(当AutoAdaptPaperSize=false时使用) -->
        <add key="ForcePaperWidth" value="827" />
        <add key="ForcePaperHeight" value="1169" />

        <!-- 数据库连接配置 -->
        <add key="OracleConnectionString" value="Data Source=localhost:1521/XE;User Id=your_username;Password=your_password;" />
    </appSettings>
    
    <system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="ShippingNoteServiceBinding" 
                         maxBufferSize="65536" 
                         maxReceivedMessageSize="65536"
                         sendTimeout="00:01:00" 
                         receiveTimeout="00:01:00">
                    <security mode="None" />
                </binding>
            </basicHttpBinding>
        </bindings>
        
        <client>
            <endpoint address="http://localhost:8080/axis2/services/ShippingNoteService"
                      binding="basicHttpBinding"
                      bindingConfiguration="ShippingNoteServiceBinding"
                      contract="ShippingNoteService.IShippingNoteService"
                      name="ShippingNoteServiceEndpoint" />
        </client>
    </system.serviceModel>
</configuration>
