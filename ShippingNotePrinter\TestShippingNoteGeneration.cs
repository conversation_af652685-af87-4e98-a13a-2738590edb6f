using System;
using System.Collections.Generic;
using ShippingNotePrinter.Services;
using ShippingNotePrinter.Models;

namespace ShippingNotePrinter
{
    /// <summary>
    /// 发运单明细生成测试类
    /// </summary>
    public class TestShippingNoteGeneration
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== 发运单明细生成测试 ===");
            
            var postBillService = new PostBillService();
            
            // 测试手机号
            string testPhoneNumber = "15167618317";
            
            try
            {
                Console.WriteLine($"测试手机号: {testPhoneNumber}");
                Console.WriteLine();
                
                // 1. 首先检查是否存在发运数据
                Console.WriteLine("1. 检查发运数据...");
                bool hasPostBill = postBillService.CheckPostBillExists(testPhoneNumber);
                Console.WriteLine($"   结果: {(hasPostBill ? "存在发运数据" : "不存在发运数据")}");
                Console.WriteLine();
                
                if (hasPostBill)
                {
                    // 2. 获取发运单明细
                    Console.WriteLine("2. 获取发运单明细...");
                    List<ShippingNote> shippingNotes = postBillService.GetShippingNotesByPhone(testPhoneNumber);

                    Console.WriteLine($"   找到 {shippingNotes.Count} 张发运单（按CARRYBILLNUMBER分组）");
                    Console.WriteLine();

                    // 显示分组信息
                    Console.WriteLine("   分组详情:");
                    foreach (var note in shippingNotes)
                    {
                        Console.WriteLine($"   - 装货单号: {note.LoadingNumber} | 发运单号: {note.ShippingNoteNumber} | 明细项数: {note.Items.Count}");
                    }
                    Console.WriteLine();
                    
                    // 3. 显示发运单列表（表头信息）
                    Console.WriteLine("3. 发运单列表（表头信息）:");
                    Console.WriteLine("".PadRight(120, '='));
                    Console.WriteLine($"{"序号",-4} {"发运单号",-20} {"客户单位",-25} {"发货仓库",-20} {"交货方式",-8} {"发运日期",-12} {"明细数量",-8}");
                    Console.WriteLine("".PadRight(120, '-'));
                    
                    for (int i = 0; i < shippingNotes.Count; i++)
                    {
                        var note = shippingNotes[i];
                        Console.WriteLine($"{i + 1,-4} {note.ShippingNoteNumber,-20} {TruncateString(note.CustomerUnit, 24),-25} {TruncateString(note.ShippingWarehouse, 19),-20} {note.DeliveryMethod,-8} {note.ShippingDate:yyyy-MM-dd},-12 {note.Items.Count,-8}");
                    }
                    Console.WriteLine("".PadRight(120, '='));
                    Console.WriteLine();
                    
                    // 4. 选择一张发运单显示详细信息
                    if (shippingNotes.Count > 0)
                    {
                        var selectedNote = shippingNotes[0];
                        Console.WriteLine($"4. 发运单详细信息 - {selectedNote.ShippingNoteNumber}:");
                        Console.WriteLine("".PadRight(80, '='));
                        Console.WriteLine($"发运单号: {selectedNote.ShippingNoteNumber}");
                        Console.WriteLine($"客户单位: {selectedNote.CustomerUnit}");
                        Console.WriteLine($"发货仓库: {selectedNote.ShippingWarehouse}");
                        Console.WriteLine($"交货方式: {selectedNote.DeliveryMethod}");
                        Console.WriteLine($"发运日期: {selectedNote.ShippingDate:yyyy-MM-dd}");
                        Console.WriteLine($"单号: {selectedNote.OrderNumber}");
                        Console.WriteLine($"打单号: {selectedNote.PrintNumber}");
                        Console.WriteLine($"联系人: {selectedNote.ContactPerson}");
                        Console.WriteLine($"电话: {selectedNote.Phone}");
                        Console.WriteLine($"市场区域: {selectedNote.MarketArea}");
                        Console.WriteLine($"收货地址: {selectedNote.DeliveryAddress}");
                        Console.WriteLine($"备注说明: {selectedNote.Remarks}");
                        Console.WriteLine($"特殊说明: {selectedNote.SpecialInstructions}");
                        Console.WriteLine($"装货单号(CARRYBILLNUMBER): {selectedNote.LoadingNumber}");
                        Console.WriteLine($"装载单号(VEHICLENUMBER): {selectedNote.VehicleNumber}");
                        Console.WriteLine($"车长: {selectedNote.VehicleLength}");
                        Console.WriteLine($"手工米数: {selectedNote.ManualMeters}");
                        Console.WriteLine($"货物重量: {selectedNote.CargoWeight}");
                        Console.WriteLine($"业务员: {selectedNote.Salesperson}");
                        Console.WriteLine($"制单: {selectedNote.Creator}");
                        Console.WriteLine($"审核: {selectedNote.Reviewer}");
                        Console.WriteLine();
                        
                        // 5. 显示物料明细（打印时才显示）
                        Console.WriteLine("5. 物料明细（打印时显示）:");
                        Console.WriteLine("".PadRight(120, '='));
                        Console.WriteLine($"{"序号",-4} {"品名规格",-40} {"单位",-6} {"数量",-10} {"单重",-8} {"标长",-10} {"总标长",-10} {"备注",-15}");
                        Console.WriteLine("".PadRight(120, '-'));
                        
                        foreach (var item in selectedNote.Items)
                        {
                            Console.WriteLine($"{item.SequenceNumber,-4} {TruncateString(item.ProductSpecification, 39),-40} {item.Unit,-6} {item.Quantity,-10:F2} {item.UnitWeight,-8:F3} {item.StandardLength,-10:F2} {item.TotalStandardLength,-10:F2} {TruncateString(item.Remarks, 14),-15}");
                        }
                        Console.WriteLine("".PadRight(120, '='));
                        
                        // 统计信息
                        decimal totalQuantity = 0;
                        decimal totalWeight = 0;
                        decimal totalStandardLength = 0;
                        
                        foreach (var item in selectedNote.Items)
                        {
                            totalQuantity += item.Quantity;
                            totalWeight += item.Quantity * item.UnitWeight;
                            totalStandardLength += item.TotalStandardLength;
                        }
                        
                        Console.WriteLine();
                        Console.WriteLine("统计信息:");
                        Console.WriteLine($"  总数量: {totalQuantity:F2}");
                        Console.WriteLine($"  总重量: {totalWeight:F3}");
                        Console.WriteLine($"  总标长: {totalStandardLength:F2}");
                        Console.WriteLine($"  明细项数: {selectedNote.Items.Count}");
                    }
                }
                else
                {
                    Console.WriteLine("2. 跳过发运单明细获取（手机号无发运数据）");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            
            Console.WriteLine();
            Console.WriteLine("测试完成，按任意键退出...");
            Console.ReadKey();
        }
        
        /// <summary>
        /// 截断字符串到指定长度
        /// </summary>
        private static string TruncateString(string input, int maxLength)
        {
            if (string.IsNullOrEmpty(input))
                return "";
                
            if (input.Length <= maxLength)
                return input;
                
            return input.Substring(0, maxLength - 3) + "...";
        }
    }
}
