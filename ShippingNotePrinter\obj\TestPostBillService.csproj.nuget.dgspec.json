{"format": 1, "restore": {"F:\\printsoft\\ShippingNotePrinter\\TestPostBillService.csproj": {}}, "projects": {"F:\\printsoft\\ShippingNotePrinter\\TestPostBillService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\printsoft\\ShippingNotePrinter\\TestPostBillService.csproj", "projectName": "TestPostBillService", "projectPath": "F:\\printsoft\\ShippingNotePrinter\\TestPostBillService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\printsoft\\ShippingNotePrinter\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}}, "frameworks": {"net48": {"dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}}}, "runtimes": {"win": {"#import": []}}}}}