using System;
using System.Collections.Generic;

namespace ShippingNotePrinter.Models
{
    /// <summary>
    /// 发运通知单模型
    /// </summary>
    public class ShippingNote
    {
        /// <summary>
        /// 发运单号
        /// </summary>
        public string ShippingNoteNumber { get; set; }

        /// <summary>
        /// 客户单位
        /// </summary>
        public string CustomerUnit { get; set; }

        /// <summary>
        /// 发货仓库
        /// </summary>
        public string ShippingWarehouse { get; set; }

        /// <summary>
        /// 交货方式
        /// </summary>
        public string DeliveryMethod { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        public DateTime ShippingDate { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 打单号
        /// </summary>
        public string PrintNumber { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactPerson { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 市场区域
        /// </summary>
        public string MarketArea { get; set; }

        /// <summary>
        /// 收货地址
        /// </summary>
        public string DeliveryAddress { get; set; }

        /// <summary>
        /// 备注说明 (DESCRIPTION)
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 特殊说明 (KDTEXTAREA)
        /// </summary>
        public string SpecialInstructions { get; set; }

        /// <summary>
        /// 发运单明细项目列表
        /// </summary>
        public List<ShippingNoteItem> Items { get; set; }

        /// <summary>
        /// 装货单号 (CARRYBILLNUMBER)
        /// </summary>
        public string LoadingNumber { get; set; }

        /// <summary>
        /// 装载单号 (VEHICLENUMBER) - 用于打印显示
        /// </summary>
        public string VehicleNumber { get; set; }

        /// <summary>
        /// 车长
        /// </summary>
        public string VehicleLength { get; set; }

        /// <summary>
        /// 手工米数
        /// </summary>
        public decimal ManualMeters { get; set; }

        /// <summary>
        /// 货物重量
        /// </summary>
        public decimal CargoWeight { get; set; }

        /// <summary>
        /// 业务员
        /// </summary>
        public string Salesperson { get; set; }

        /// <summary>
        /// 主管
        /// </summary>
        public string Supervisor { get; set; }

        /// <summary>
        /// 制单
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 审核
        /// </summary>
        public string Reviewer { get; set; }

        /// <summary>
        /// 制单日期
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 是否加急
        /// </summary>
        public bool IsUrgent { get; set; }

        /// <summary>
        /// 二维码内容
        /// </summary>
        public string QRCodeContent { get; set; }

        /// <summary>
        /// 条形码内容
        /// </summary>
        public string BarcodeContent { get; set; }

        /// <summary>
        /// NO字段内容 (POSTREQUISITIONNUMBER)
        /// </summary>
        public string PostRequisitionNumber { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; } = 1;

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; } = 1;

        /// <summary>
        /// 所有页面的总重量（用于显示总量总计）
        /// </summary>
        public decimal TotalWeight { get; set; }

        public ShippingNote()
        {
            Items = new List<ShippingNoteItem>();
            ShippingDate = DateTime.Now;
            CreateDate = DateTime.Now;
        }
    }
}
