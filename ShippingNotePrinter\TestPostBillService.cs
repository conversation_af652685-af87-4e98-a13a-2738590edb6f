using System;
using ShippingNotePrinter.Services;

namespace ShippingNotePrinter
{
    /// <summary>
    /// PostBillService测试类
    /// </summary>
    public class TestPostBillService
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== PostBillService 测试 ===");
            
            var postBillService = new PostBillService();
            
            // 测试手机号
            string testPhoneNumber = "15167618317";
            
            try
            {
                Console.WriteLine($"测试手机号: {testPhoneNumber}");
                Console.WriteLine();
                
                // 测试 getPostBill
                Console.WriteLine("1. 测试 getPostBill 接口...");
                bool hasPostBill = postBillService.CheckPostBillExists(testPhoneNumber);
                Console.WriteLine($"   结果: {(hasPostBill ? "存在发运数据" : "不存在发运数据")}");
                Console.WriteLine();
                
                if (hasPostBill)
                {
                    // 测试 addkaptcha
                    Console.WriteLine("2. 测试 addkaptcha 接口...");
                    bool sendSuccess = postBillService.SendVerificationCode(testPhoneNumber);
                    Console.WriteLine($"   结果: {(sendSuccess ? "验证码发送成功" : "验证码发送失败")}");

                    if (sendSuccess)
                    {
                        // 测试验证码验证
                        Console.WriteLine();
                        Console.WriteLine("3. 测试 checkkaptcha 接口...");
                        Console.Write("请输入收到的验证码: ");
                        string inputCode = Console.ReadLine();

                        if (!string.IsNullOrEmpty(inputCode))
                        {
                            bool verifySuccess = postBillService.VerifyCode(inputCode);
                            Console.WriteLine($"   结果: {(verifySuccess ? "验证码校验成功" : "验证码校验失败")}");
                        }
                        else
                        {
                            Console.WriteLine("   跳过验证码校验测试（未输入验证码）");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("2. 跳过验证码发送测试（手机号无发运数据）");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            
            Console.WriteLine();
            Console.WriteLine("测试完成，按任意键退出...");
            Console.ReadKey();
        }
    }
}
